/**
 * Adapter layer that wraps official Lexical functions and provides
 * compatibility with our existing API and error handling
 */

import {
  $convertFromMarkdownString,
  $convertToMarkdownString,
  type Transformer,
} from '@lexical/markdown';
import {
  createEditor,
  $getRoot,
  $createParagraphNode,
  $createTextNode,
  type LexicalEditor,
  type EditorState,
  type LexicalNode,
} from 'lexical';
import { $createHeadingNode, $createQuoteNode } from '@lexical/rich-text';
import { $createListNode, $createListItemNode } from '@lexical/list';
import { $createCodeNode } from '@lexical/code';
import { $createLinkNode } from '@lexical/link';

import type { LexicalDocument } from '../types';
import type { ConversionOptions } from './parser-config';
import type { MarkdownToLexicalResult, LexicalToMarkdownResult } from './conversion-types';
import { ConversionResultBuilder } from './conversion-types';

/**
 * Editor configuration for Lexical context
 */
interface EditorConfig {
  namespace: string;
  nodes: any[];
  onError: (error: Error) => void;
  theme?: Record<string, string>;
}

/**
 * Lexical context adapter for managing editor instances and conversions
 */
export class LexicalAdapter {
  private static editorPool: LexicalEditor[] = [];
  private static maxPoolSize = 5;
  private static defaultConfig: EditorConfig = {
    namespace: 'ghost-obsidian-sync',
    nodes: [
      $createParagraphNode,
      $createTextNode,
      $createHeadingNode,
      $createListNode,
      $createListItemNode,
      $createQuoteNode,
      $createCodeNode,
      $createLinkNode,
    ],
    onError: (error: Error) => {
      console.error('Lexical editor error:', error);
    },
    theme: {},
  };

  /**
   * Convert markdown to Lexical document using official package
   */
  static async convertFromMarkdown(
    markdown: string,
    transformers: Transformer[],
    options: ConversionOptions = {}
  ): Promise<MarkdownToLexicalResult> {
    const builder = new ConversionResultBuilder();

    try {
      // Input validation
      if (!markdown || typeof markdown !== 'string') {
        return builder.error('INVALID_INPUT', 'Markdown input must be a non-empty string');
      }

      if (markdown.length > (options.performance?.largeDocumentThreshold || 50000)) {
        builder.addWarning('Large document detected, performance may be affected');
      }

      // Execute conversion in Lexical context
      const result = await this.executeInContext(async (editor) => {
        const root = $getRoot();
        root.clear();

        // Use official conversion function
        $convertFromMarkdownString(
          markdown,
          transformers,
          root,
          options.shouldPreserveNewLines,
          options.shouldMergeAdjacentLines
        );

        // Extract Lexical document from editor state
        return this.extractLexicalDocument(root);
      });

      builder.setNodeCount(this.countNodes(result));
      return builder.success(result);

    } catch (error) {
      return builder.error(
        'CONVERSION_FAILED',
        error instanceof Error ? error.message : 'Unknown conversion error',
        { originalError: error },
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Convert Lexical document to markdown using official package
   */
  static async convertToMarkdown(
    document: LexicalDocument,
    transformers: Transformer[],
    options: ConversionOptions = {}
  ): Promise<LexicalToMarkdownResult> {
    const builder = new ConversionResultBuilder();

    try {
      // Input validation
      if (!document?.root) {
        return builder.error('INVALID_INPUT', 'Document must have a root node');
      }

      // Execute conversion in Lexical context
      const result = await this.executeInContext(async (editor) => {
        const root = $getRoot();
        root.clear();

        // Populate editor with document content
        this.populateEditorFromDocument(root, document);

        // Use official conversion function
        return $convertToMarkdownString(
          transformers,
          root,
          options.shouldPreserveNewLines
        );
      });

      builder.setNodeCount(this.countNodes(document));
      return builder.success(result);

    } catch (error) {
      return builder.error(
        'CONVERSION_FAILED',
        error instanceof Error ? error.message : 'Unknown conversion error',
        { originalError: error },
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Execute function within Lexical editor context
   */
  private static async executeInContext<T>(
    fn: (editor: LexicalEditor) => T | Promise<T>
  ): Promise<T> {
    const editor = this.getEditor();
    try {
      return await editor.update(() => fn(editor));
    } finally {
      this.returnEditor(editor);
    }
  }

  /**
   * Get editor from pool or create new one
   */
  private static getEditor(): LexicalEditor {
    if (this.editorPool.length > 0) {
      return this.editorPool.pop()!;
    }

    return createEditor(this.defaultConfig);
  }

  /**
   * Return editor to pool
   */
  private static returnEditor(editor: LexicalEditor): void {
    if (this.editorPool.length < this.maxPoolSize) {
      // Clear editor state before returning to pool
      editor.update(() => {
        const root = $getRoot();
        root.clear();
      });
      this.editorPool.push(editor);
    }
    // If pool is full, let editor be garbage collected
  }

  /**
   * Extract Lexical document from editor root
   */
  private static extractLexicalDocument(root: any): LexicalDocument {
    const children = root.getChildren().map((child: any) => this.serializeLexicalNode(child));

    return {
      root: {
        type: 'root',
        children,
        direction: root.getDirection() || 'ltr',
        format: root.getFormat() || '',
        indent: root.getIndent() || 0,
        version: 1,
      },
    };
  }

  /**
   * Serialize Lexical node to our format
   */
  private static serializeLexicalNode(node: any): any {
    const baseNode = {
      type: node.getType(),
      version: node.getVersion() || 1,
    };

    // Handle different node types
    switch (node.getType()) {
      case 'text':
        return {
          ...baseNode,
          text: node.getTextContent(),
          detail: node.getDetail() || 0,
          format: node.getFormat() || 0,
          mode: node.getMode() || 'normal',
          style: node.getStyle() || '',
        };

      case 'paragraph':
      case 'heading':
      case 'quote':
      case 'list':
      case 'listitem':
        const elementNode = {
          ...baseNode,
          children: node.getChildren().map((child: any) => this.serializeLexicalNode(child)),
          direction: node.getDirection() || 'ltr',
          format: node.getFormat() || '',
          indent: node.getIndent() || 0,
        };

        // Add type-specific properties
        if (node.getType() === 'heading') {
          (elementNode as any).tag = node.getTag();
        } else if (node.getType() === 'list') {
          (elementNode as any).listType = node.getListType();
          (elementNode as any).start = node.getStart();
        } else if (node.getType() === 'listitem') {
          (elementNode as any).checked = node.getChecked();
          (elementNode as any).value = node.getValue();
        }

        return elementNode;

      case 'code':
        return {
          ...baseNode,
          language: node.getLanguage() || '',
          text: node.getTextContent(),
        };

      case 'link':
        return {
          ...baseNode,
          url: node.getURL(),
          title: node.getTitle() || '',
          target: node.getTarget() || '',
          rel: node.getRel() || '',
          children: node.getChildren().map((child: any) => this.serializeLexicalNode(child)),
        };

      default:
        // Generic node handling
        const genericNode: any = { ...baseNode };

        // Try to get common properties
        if (typeof node.getTextContent === 'function') {
          genericNode.text = node.getTextContent();
        }

        if (typeof node.getChildren === 'function') {
          genericNode.children = node.getChildren().map((child: any) => this.serializeLexicalNode(child));
        }

        return genericNode;
    }
  }

  /**
   * Populate editor from Lexical document
   */
  private static populateEditorFromDocument(root: any, document: LexicalDocument): void {
    for (const child of document.root.children) {
      const node = this.createLexicalNodeFromSerialized(child);
      if (node) {
        root.append(node);
      }
    }
  }

  /**
   * Create Lexical node from serialized format
   */
  private static createLexicalNodeFromSerialized(serialized: any): any {
    switch (serialized.type) {
      case 'text':
        const textNode = $createTextNode(serialized.text || '');
        if (serialized.format) {
          textNode.setFormat(serialized.format);
        }
        if (serialized.style) {
          textNode.setStyle(serialized.style);
        }
        return textNode;

      case 'paragraph':
        const paragraphNode = $createParagraphNode();
        if (serialized.children) {
          for (const child of serialized.children) {
            const childNode = this.createLexicalNodeFromSerialized(child);
            if (childNode) {
              paragraphNode.append(childNode);
            }
          }
        }
        return paragraphNode;

      case 'heading':
        const headingNode = $createHeadingNode(serialized.tag || 'h1');
        if (serialized.children) {
          for (const child of serialized.children) {
            const childNode = this.createLexicalNodeFromSerialized(child);
            if (childNode) {
              headingNode.append(childNode);
            }
          }
        }
        return headingNode;

      case 'quote':
        const quoteNode = $createQuoteNode();
        if (serialized.children) {
          for (const child of serialized.children) {
            const childNode = this.createLexicalNodeFromSerialized(child);
            if (childNode) {
              quoteNode.append(childNode);
            }
          }
        }
        return quoteNode;

      case 'list':
        const listNode = $createListNode(serialized.listType || 'bullet', serialized.start);
        if (serialized.children) {
          for (const child of serialized.children) {
            const childNode = this.createLexicalNodeFromSerialized(child);
            if (childNode) {
              listNode.append(childNode);
            }
          }
        }
        return listNode;

      case 'listitem':
        const listItemNode = $createListItemNode(serialized.checked);
        if (serialized.children) {
          for (const child of serialized.children) {
            const childNode = this.createLexicalNodeFromSerialized(child);
            if (childNode) {
              listItemNode.append(childNode);
            }
          }
        }
        return listItemNode;

      case 'code':
        return $createCodeNode(serialized.language || '');

      case 'link':
        const linkNode = $createLinkNode(serialized.url, {
          title: serialized.title,
          target: serialized.target,
          rel: serialized.rel,
        });
        if (serialized.children) {
          for (const child of serialized.children) {
            const childNode = this.createLexicalNodeFromSerialized(child);
            if (childNode) {
              linkNode.append(childNode);
            }
          }
        }
        return linkNode;

      default:
        // For unknown node types, create a text node with the content
        return $createTextNode(serialized.text || serialized.value || '');
    }
  }

  /**
   * Count nodes in document for metadata
   */
  private static countNodes(document: LexicalDocument | any): number {
    if (!document?.root?.children) {
      return 0;
    }

    let count = 1; // Root node
    const countRecursive = (children: any[]): void => {
      for (const child of children) {
        count++;
        if (child.children) {
          countRecursive(child.children);
        }
      }
    };

    countRecursive(document.root.children);
    return count;
  }

  /**
   * Clear editor pool (for cleanup)
   */
  static clearPool(): void {
    this.editorPool = [];
  }

  /**
   * Get pool statistics
   */
  static getPoolStats(): { size: number; maxSize: number } {
    return {
      size: this.editorPool.length,
      maxSize: this.maxPoolSize,
    };
  }
}
