/**
 * TypeScript types and interfaces for conversion results, errors, and metadata
 * Based on the JSON specification for structured error handling and result reporting
 */

import type { LexicalDocument } from '../types';

/**
 * Conversion error codes as defined in JSON specification
 */
export type ConversionErrorCode = 
  | 'NULL_INPUT'
  | 'INVALID_INPUT'
  | 'DOCUMENT_TOO_LARGE'
  | 'PARSE_FAILED'
  | 'CONVERSION_FAILED'
  | 'TRANSFORMER_ERROR'
  | 'UNEXPECTED_ERROR';

/**
 * Detailed conversion error information
 */
export interface ConversionError {
  /** Error code for programmatic handling */
  code: ConversionErrorCode;
  /** Human-readable error message */
  message: string;
  /** Additional error details and context */
  details?: Record<string, any>;
  /** Stack trace for debugging (development only) */
  stack?: string;
  /** Source location where error occurred */
  source?: {
    line?: number;
    column?: number;
    nodeType?: string;
    transformerId?: string;
  };
  /** Timestamp when error occurred */
  timestamp?: number;
}

/**
 * Conversion metadata for performance monitoring and debugging
 */
export interface ConversionMetadata {
  /** Time taken for conversion in milliseconds */
  processingTime?: number;
  /** Number of nodes processed during conversion */
  nodeCount?: number;
  /** List of transformer IDs that were used */
  transformersUsed?: string[];
  /** Number of times fallback conversion was used */
  fallbacksUsed?: number;
  /** Memory usage statistics */
  memoryUsage?: {
    heapUsed?: number;
    heapTotal?: number;
    external?: number;
  };
  /** Performance breakdown by phase */
  performanceBreakdown?: {
    parsing?: number;
    transformation?: number;
    serialization?: number;
    validation?: number;
  };
  /** Cache statistics */
  cacheStats?: {
    hits?: number;
    misses?: number;
    hitRate?: number;
  };
}

/**
 * Generic conversion result wrapper
 */
export interface ConversionResult<T> {
  /** Whether the conversion was successful */
  success: boolean;
  /** Converted data (only present if success is true) */
  data?: T;
  /** Non-fatal warnings during conversion */
  warnings?: string[];
  /** Errors that occurred during conversion */
  errors?: ConversionError[];
  /** Metadata about the conversion process */
  metadata?: ConversionMetadata;
}

/**
 * Specific result type for markdown to Lexical conversion
 */
export type MarkdownToLexicalResult = ConversionResult<LexicalDocument>;

/**
 * Specific result type for Lexical to markdown conversion
 */
export type LexicalToMarkdownResult = ConversionResult<string>;

/**
 * Utility class for creating conversion results
 */
export class ConversionResultBuilder {
  private result: ConversionResult<any>;
  private startTime: number;

  constructor() {
    this.result = {
      success: false,
      warnings: [],
      errors: [],
      metadata: {},
    };
    this.startTime = performance.now();
  }

  /**
   * Mark conversion as successful with data
   */
  success<T>(data: T): ConversionResult<T> {
    this.result.success = true;
    this.result.data = data;
    this.finalizeMetadata();
    return this.result as ConversionResult<T>;
  }

  /**
   * Mark conversion as failed with error
   */
  error<T>(
    code: ConversionErrorCode,
    message: string,
    details?: Record<string, any>,
    stack?: string
  ): ConversionResult<T> {
    this.result.success = false;
    this.addError(code, message, details, stack);
    this.finalizeMetadata();
    return this.result as ConversionResult<T>;
  }

  /**
   * Add a warning message
   */
  addWarning(message: string): this {
    if (!this.result.warnings) {
      this.result.warnings = [];
    }
    this.result.warnings.push(message);
    return this;
  }

  /**
   * Add an error
   */
  addError(
    code: ConversionErrorCode,
    message: string,
    details?: Record<string, any>,
    stack?: string,
    source?: ConversionError['source']
  ): this {
    if (!this.result.errors) {
      this.result.errors = [];
    }
    
    this.result.errors.push({
      code,
      message,
      details,
      stack,
      source,
      timestamp: Date.now(),
    });
    return this;
  }

  /**
   * Set metadata
   */
  setMetadata(metadata: Partial<ConversionMetadata>): this {
    this.result.metadata = {
      ...this.result.metadata,
      ...metadata,
    };
    return this;
  }

  /**
   * Add transformer to used list
   */
  addTransformerUsed(transformerId: string): this {
    if (!this.result.metadata) {
      this.result.metadata = {};
    }
    if (!this.result.metadata.transformersUsed) {
      this.result.metadata.transformersUsed = [];
    }
    if (!this.result.metadata.transformersUsed.includes(transformerId)) {
      this.result.metadata.transformersUsed.push(transformerId);
    }
    return this;
  }

  /**
   * Increment fallback usage counter
   */
  incrementFallbacks(): this {
    if (!this.result.metadata) {
      this.result.metadata = {};
    }
    this.result.metadata.fallbacksUsed = (this.result.metadata.fallbacksUsed || 0) + 1;
    return this;
  }

  /**
   * Set node count
   */
  setNodeCount(count: number): this {
    if (!this.result.metadata) {
      this.result.metadata = {};
    }
    this.result.metadata.nodeCount = count;
    return this;
  }

  /**
   * Finalize metadata with processing time
   */
  private finalizeMetadata(): void {
    if (!this.result.metadata) {
      this.result.metadata = {};
    }
    this.result.metadata.processingTime = performance.now() - this.startTime;
    
    // Add memory usage if available
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const memUsage = process.memoryUsage();
      this.result.metadata.memoryUsage = {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
      };
    }
  }
}

/**
 * Utility functions for working with conversion results
 */
export class ConversionResultUtils {
  /**
   * Check if result is successful
   */
  static isSuccess<T>(result: ConversionResult<T>): result is ConversionResult<T> & { data: T } {
    return result.success && result.data !== undefined;
  }

  /**
   * Check if result has errors
   */
  static hasErrors<T>(result: ConversionResult<T>): boolean {
    return !result.success || (result.errors && result.errors.length > 0);
  }

  /**
   * Check if result has warnings
   */
  static hasWarnings<T>(result: ConversionResult<T>): boolean {
    return result.warnings && result.warnings.length > 0;
  }

  /**
   * Get all error messages
   */
  static getErrorMessages<T>(result: ConversionResult<T>): string[] {
    return result.errors?.map(error => error.message) || [];
  }

  /**
   * Get errors by code
   */
  static getErrorsByCode<T>(
    result: ConversionResult<T>,
    code: ConversionErrorCode
  ): ConversionError[] {
    return result.errors?.filter(error => error.code === code) || [];
  }

  /**
   * Create a simple success result
   */
  static success<T>(data: T, metadata?: ConversionMetadata): ConversionResult<T> {
    return {
      success: true,
      data,
      warnings: [],
      errors: [],
      metadata,
    };
  }

  /**
   * Create a simple error result
   */
  static error<T>(
    code: ConversionErrorCode,
    message: string,
    details?: Record<string, any>
  ): ConversionResult<T> {
    return {
      success: false,
      warnings: [],
      errors: [{
        code,
        message,
        details,
        timestamp: Date.now(),
      }],
      metadata: {},
    };
  }

  /**
   * Merge multiple results (for batch operations)
   */
  static merge<T>(results: ConversionResult<T>[]): ConversionResult<T[]> {
    const builder = new ConversionResultBuilder();
    const data: T[] = [];
    let hasErrors = false;

    for (const result of results) {
      if (result.success && result.data !== undefined) {
        data.push(result.data);
      } else {
        hasErrors = true;
      }

      // Merge warnings
      if (result.warnings) {
        result.warnings.forEach(warning => builder.addWarning(warning));
      }

      // Merge errors
      if (result.errors) {
        result.errors.forEach(error => {
          builder.addError(error.code, error.message, error.details, error.stack, error.source);
        });
      }

      // Merge metadata
      if (result.metadata) {
        if (result.metadata.transformersUsed) {
          result.metadata.transformersUsed.forEach(id => builder.addTransformerUsed(id));
        }
        if (result.metadata.fallbacksUsed) {
          for (let i = 0; i < result.metadata.fallbacksUsed; i++) {
            builder.incrementFallbacks();
          }
        }
      }
    }

    if (hasErrors) {
      return builder.error('CONVERSION_FAILED', 'One or more conversions failed');
    } else {
      return builder.success(data);
    }
  }

  /**
   * Convert result to JSON for logging/debugging
   */
  static toJSON<T>(result: ConversionResult<T>): string {
    // Create a serializable version (remove functions, circular refs, etc.)
    const serializable = {
      success: result.success,
      hasData: result.data !== undefined,
      dataType: result.data ? typeof result.data : undefined,
      warnings: result.warnings,
      errors: result.errors?.map(error => ({
        code: error.code,
        message: error.message,
        details: error.details,
        source: error.source,
        timestamp: error.timestamp,
        // Exclude stack trace for security
      })),
      metadata: result.metadata,
    };

    return JSON.stringify(serializable, null, 2);
  }

  /**
   * Create a performance summary from metadata
   */
  static getPerformanceSummary(metadata?: ConversionMetadata): string {
    if (!metadata) {
      return 'No performance data available';
    }

    const parts: string[] = [];
    
    if (metadata.processingTime) {
      parts.push(`${metadata.processingTime.toFixed(2)}ms`);
    }
    
    if (metadata.nodeCount) {
      parts.push(`${metadata.nodeCount} nodes`);
    }
    
    if (metadata.transformersUsed) {
      parts.push(`${metadata.transformersUsed.length} transformers`);
    }
    
    if (metadata.fallbacksUsed) {
      parts.push(`${metadata.fallbacksUsed} fallbacks`);
    }

    if (metadata.cacheStats?.hitRate) {
      parts.push(`${(metadata.cacheStats.hitRate * 100).toFixed(1)}% cache hit rate`);
    }

    return parts.join(', ') || 'No performance metrics available';
  }
}
