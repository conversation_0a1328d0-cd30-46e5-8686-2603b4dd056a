/**
 * Math expression transformers for Obsidian
 * Handles inline ($...$) and block ($$...$$) math expressions
 */

import type { TextMatchTransformer, MultilineElementTransformer } from '@lexical/markdown';
import {
  $createParagraphNode,
  $createTextNode,
  ElementNode,
  LexicalNode,
  TextNode,
} from 'lexical';

/**
 * Math display modes
 */
export type MathDisplayMode = 'inline' | 'block';

/**
 * Math node for LaTeX expressions
 */
export class MathNode extends ElementNode {
  __latex: string;
  __displayMode: MathDisplayMode;

  constructor(latex: string, displayMode: MathDisplayMode = 'inline', key?: string) {
    super(key);
    this.__latex = latex;
    this.__displayMode = displayMode;
  }

  static getType(): string {
    return 'math';
  }

  static clone(node: MathNode): MathNode {
    return new MathNode(node.__latex, node.__displayMode, node.__key);
  }

  getLatex(): string {
    return this.__latex;
  }

  setLatex(latex: string): this {
    const writable = this.getWritable();
    writable.__latex = latex;
    return writable;
  }

  getDisplayMode(): MathDisplayMode {
    return this.__displayMode;
  }

  setDisplayMode(displayMode: MathDisplayMode): this {
    const writable = this.getWritable();
    writable.__displayMode = displayMode;
    return writable;
  }

  createDOM(config: any): HTMLElement {
    const element = document.createElement(this.__displayMode === 'block' ? 'div' : 'span');
    element.className = `math-${this.__displayMode}`;
    element.setAttribute('data-latex', this.__latex);
    
    // For now, just display the LaTeX source
    // In a real implementation, you'd render with KaTeX or MathJax
    if (this.__displayMode === 'block') {
      element.textContent = `$$${this.__latex}$$`;
      element.style.display = 'block';
      element.style.textAlign = 'center';
      element.style.margin = '1em 0';
    } else {
      element.textContent = `$${this.__latex}$`;
      element.style.display = 'inline';
    }
    
    return element;
  }

  updateDOM(): false {
    return false;
  }

  exportJSON(): any {
    return {
      type: 'math',
      latex: this.__latex,
      displayMode: this.__displayMode,
      version: 1,
    };
  }

  static importJSON(serializedNode: any): MathNode {
    const { latex, displayMode } = serializedNode;
    return $createMathNode(latex, displayMode);
  }

  isInline(): boolean {
    return this.__displayMode === 'inline';
  }

  isKeyboardSelectable(): true {
    return true;
  }

  canBeEmpty(): false {
    return false;
  }

  canInsertTextBefore(): false {
    return false;
  }

  canInsertTextAfter(): false {
    return false;
  }
}

/**
 * Create math node
 */
export function $createMathNode(latex: string, displayMode: MathDisplayMode = 'inline'): MathNode {
  return new MathNode(latex, displayMode);
}

/**
 * Check if node is math node
 */
export function $isMathNode(node: LexicalNode | null | undefined): node is MathNode {
  return node instanceof MathNode;
}

/**
 * Inline math transformer for $...$ syntax
 */
export const MATH_INLINE: TextMatchTransformer = {
  dependencies: [MathNode],
  export: (
    node: LexicalNode,
    exportChildren: (node: any) => string,
    exportFormat: (node: TextNode, textContent: string) => string
  ): string | null => {
    if (!$isMathNode(node)) {
      return null;
    }

    if (node.getDisplayMode() === 'inline') {
      return `$${node.getLatex()}$`;
    }

    return null;
  },
  importRegExp: /\$([^$\n]+)\$/,
  replace: (textNode: TextNode, match: RegExpMatchArray): void => {
    const latex = match[1];
    if (!latex.trim()) {
      return;
    }

    const mathNode = $createMathNode(latex.trim(), 'inline');
    textNode.replace(mathNode);
  },
  trigger: '$',
  type: 'text-match',
};

/**
 * Block math transformer for $$...$$ syntax
 */
export const MATH_BLOCK: MultilineElementTransformer = {
  dependencies: [MathNode],
  export: (node: LexicalNode, traverseChildren: (node: ElementNode) => string): string | null => {
    if (!$isMathNode(node)) {
      return null;
    }

    if (node.getDisplayMode() === 'block') {
      return `$$\n${node.getLatex()}\n$$`;
    }

    return null;
  },
  regExpStart: /^\$\$\s*$/,
  regExpEnd: /^\$\$\s*$/,
  replace: (
    rootNode: ElementNode,
    children: Array<LexicalNode>,
    startMatch: Array<string>,
    endMatch: Array<string>,
    linesInBetween?: Array<string>,
    isImport?: boolean
  ): boolean | void => {
    const latex = linesInBetween?.join('\n').trim() || '';
    
    if (!latex) {
      return false;
    }

    const mathNode = $createMathNode(latex, 'block');
    rootNode.append(mathNode);

    if (!isImport) {
      mathNode.select();
    }

    return true;
  },
  type: 'multiline-element',
};

/**
 * Alternative block math transformer for display math
 * Handles \[...\] syntax common in LaTeX
 */
export const MATH_DISPLAY: MultilineElementTransformer = {
  dependencies: [MathNode],
  export: (node: LexicalNode, traverseChildren: (node: ElementNode) => string): string | null => {
    if (!$isMathNode(node)) {
      return null;
    }

    if (node.getDisplayMode() === 'block') {
      // Export as $$ format for consistency
      return `$$\n${node.getLatex()}\n$$`;
    }

    return null;
  },
  regExpStart: /^\\\[\s*$/,
  regExpEnd: /^\\\]\s*$/,
  replace: (
    rootNode: ElementNode,
    children: Array<LexicalNode>,
    startMatch: Array<string>,
    endMatch: Array<string>,
    linesInBetween?: Array<string>,
    isImport?: boolean
  ): boolean | void => {
    const latex = linesInBetween?.join('\n').trim() || '';
    
    if (!latex) {
      return false;
    }

    const mathNode = $createMathNode(latex, 'block');
    rootNode.append(mathNode);

    if (!isImport) {
      mathNode.select();
    }

    return true;
  },
  type: 'multiline-element',
};

/**
 * Utility functions for math operations
 */
export const MathUtils = {
  /**
   * Validate LaTeX syntax (basic validation)
   */
  isValidLatex(latex: string): boolean {
    if (!latex || typeof latex !== 'string') {
      return false;
    }

    // Basic validation: check for balanced braces
    let braceCount = 0;
    for (const char of latex) {
      if (char === '{') {
        braceCount++;
      } else if (char === '}') {
        braceCount--;
        if (braceCount < 0) {
          return false;
        }
      }
    }

    return braceCount === 0;
  },

  /**
   * Clean LaTeX expression
   */
  cleanLatex(latex: string): string {
    return latex
      .trim()
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/\n\s*/g, '\n'); // Preserve line breaks but clean indentation
  },

  /**
   * Extract math expressions from text
   */
  extractMathExpressions(text: string): Array<{ latex: string; displayMode: MathDisplayMode; start: number; end: number }> {
    const expressions: Array<{ latex: string; displayMode: MathDisplayMode; start: number; end: number }> = [];
    
    // Find block math expressions first ($$...$$)
    const blockRegex = /\$\$([\s\S]*?)\$\$/g;
    let match;
    
    while ((match = blockRegex.exec(text)) !== null) {
      expressions.push({
        latex: match[1].trim(),
        displayMode: 'block',
        start: match.index,
        end: match.index + match[0].length,
      });
    }
    
    // Find inline math expressions ($...$) that don't overlap with block expressions
    const inlineRegex = /\$([^$\n]+)\$/g;
    
    while ((match = inlineRegex.exec(text)) !== null) {
      const start = match.index;
      const end = match.index + match[0].length;
      
      // Check if this overlaps with any block expression
      const overlaps = expressions.some(expr => 
        (start >= expr.start && start < expr.end) || 
        (end > expr.start && end <= expr.end)
      );
      
      if (!overlaps) {
        expressions.push({
          latex: match[1].trim(),
          displayMode: 'inline',
          start,
          end,
        });
      }
    }
    
    return expressions.sort((a, b) => a.start - b.start);
  },
};
