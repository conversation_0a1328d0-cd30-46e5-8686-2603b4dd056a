/**
 * Enhanced table transformer for Ghost and Obsidian table features
 * Uses official @lexical/table package with custom enhancements
 */

import type { ElementTransformer } from '@lexical/markdown';
import {
  $createTableNode,
  $createTableRowNode,
  $createTableCellNode,
  $isTableNode,
  $isTableRowNode,
  $isTableCellNode,
  TableNode,
  TableRowNode,
  TableCellNode,
  TableCellHeaderStates,
} from '@lexical/table';
import {
  $createParagraphNode,
  $createTextNode,
  $isElementNode,
  ElementNode,
  LexicalNode,
  TextNode,
} from 'lexical';

/**
 * Table alignment options
 */
export type TableAlignment = 'left' | 'center' | 'right' | null;

/**
 * Enhanced table cell node with Ghost/Obsidian features
 */
export class EnhancedTableCellNode extends TableCellNode {
  __alignment: TableAlignment;

  constructor(
    headerState?: number,
    colSpan?: number,
    width?: number,
    alignment?: TableAlignment,
    key?: string
  ) {
    super(headerState, colSpan, width, key);
    this.__alignment = alignment || null;
  }

  static getType(): string {
    return 'enhanced-table-cell';
  }

  static clone(node: EnhancedTableCellNode): EnhancedTableCellNode {
    return new EnhancedTableCellNode(
      node.__headerState,
      node.__colSpan,
      node.__width,
      node.__alignment,
      node.__key
    );
  }

  getAlignment(): TableAlignment {
    return this.__alignment;
  }

  setAlignment(alignment: TableAlignment): this {
    const writable = this.getWritable();
    writable.__alignment = alignment;
    return writable;
  }

  createDOM(config: any): HTMLElement {
    const element = super.createDOM(config);

    if (this.__alignment) {
      element.style.textAlign = this.__alignment;
    }

    return element;
  }

  exportJSON(): any {
    return {
      ...super.exportJSON(),
      alignment: this.__alignment,
    };
  }

  static importJSON(serializedNode: any): EnhancedTableCellNode {
    const { headerState, colSpan, width, alignment } = serializedNode;
    return $createEnhancedTableCellNode(headerState, colSpan, width, alignment);
  }
}

/**
 * Create enhanced table cell node
 */
export function $createEnhancedTableCellNode(
  headerState?: number,
  colSpan?: number,
  width?: number,
  alignment?: TableAlignment
): EnhancedTableCellNode {
  return new EnhancedTableCellNode(headerState, colSpan, width, alignment);
}

/**
 * Check if node is enhanced table cell
 */
export function $isEnhancedTableCellNode(
  node: LexicalNode | null | undefined
): node is EnhancedTableCellNode {
  return node instanceof EnhancedTableCellNode;
}

/**
 * Parse table alignment from markdown syntax
 */
function parseAlignment(alignText: string): TableAlignment {
  const trimmed = alignText.trim();
  if (trimmed.startsWith(':') && trimmed.endsWith(':')) {
    return 'center';
  } else if (trimmed.endsWith(':')) {
    return 'right';
  } else if (trimmed.startsWith(':')) {
    return 'left';
  }
  return null;
}

/**
 * Format alignment for markdown export
 */
function formatAlignment(alignment: TableAlignment): string {
  switch (alignment) {
    case 'center':
      return ':---:';
    case 'right':
      return '---:';
    case 'left':
      return ':---';
    default:
      return '---';
  }
}

/**
 * Extract text content from table cell
 */
function extractCellText(cell: TableCellNode): string {
  const textParts: string[] = [];

  function traverse(node: LexicalNode): void {
    if (node.getType() === 'text') {
      textParts.push((node as TextNode).getTextContent());
    } else if ($isElementNode(node)) {
      const children = node.getChildren();
      children.forEach(traverse);
    }
  }

  const children = cell.getChildren();
  children.forEach(traverse);

  return textParts.join('').trim();
}

/**
 * Create table cell from text content
 */
function createTableCellFromText(
  text: string,
  isHeader: boolean = false,
  alignment: TableAlignment = null
): TableCellNode {
  const headerState = isHeader ? TableCellHeaderStates.ROW : TableCellHeaderStates.NO_HEADER;
  const cell = $isEnhancedTableCellNode
    ? $createEnhancedTableCellNode(headerState, 1, undefined, alignment)
    : $createTableCellNode(headerState);

  if (text.trim()) {
    const paragraph = $createParagraphNode();
    paragraph.append($createTextNode(text.trim()));
    cell.append(paragraph);
  } else {
    // Empty cell needs a paragraph to be valid
    cell.append($createParagraphNode());
  }

  return cell;
}

/**
 * Regular expressions for table parsing
 */
const TABLE_ROW_REG_EXP = /^(?:\|)(.+)(?:\|)\s?$/;
const TABLE_ROW_DIVIDER_REG_EXP = /^(\| ?:?-*:? ?)+\|\s?$/;

/**
 * Enhanced table transformer with Ghost/Obsidian features
 */
export const ENHANCED_TABLE: ElementTransformer = {
  dependencies: [TableNode, TableRowNode, TableCellNode, EnhancedTableCellNode],
  export: (node: LexicalNode): string | null => {
    if (!$isTableNode(node)) {
      return null;
    }

    const rows = node.getChildren();
    if (rows.length === 0) {
      return null;
    }

    const output: string[] = [];
    let alignments: TableAlignment[] = [];
    let hasHeader = false;

    // Process each row
    rows.forEach((row, rowIndex) => {
      if (!$isTableRowNode(row)) {
        return;
      }

      const cells = row.getChildren();
      const cellTexts: string[] = [];

      cells.forEach((cell, cellIndex) => {
        if (!$isTableCellNode(cell)) {
          return;
        }

        const text = extractCellText(cell);
        cellTexts.push(text.replace(/\|/g, '\\|')); // Escape pipes

        // Check if this is a header cell
        if (rowIndex === 0 && cell.hasHeader()) {
          hasHeader = true;
        }

        // Collect alignment information from enhanced cells
        if ($isEnhancedTableCellNode(cell) && rowIndex === 0) {
          alignments[cellIndex] = cell.getAlignment();
        }
      });

      // Format row
      if (cellTexts.length > 0) {
        output.push(`| ${cellTexts.join(' | ')} |`);

        // Add separator row after header
        if (rowIndex === 0 && hasHeader) {
          const separators = cellTexts.map((_, index) =>
            formatAlignment(alignments[index] || null)
          );
          output.push(`| ${separators.join(' | ')} |`);
        }
      }
    });

    return output.join('\n');
  },
  regExp: TABLE_ROW_REG_EXP,
  replace: (
    parentNode: ElementNode,
    children: Array<LexicalNode>,
    match: Array<string>,
    isImport: boolean
  ): boolean | void => {
    // This will be called for each table row
    // We need to collect multiple rows to build a complete table

    const rowText = match[1];
    if (!rowText) {
      return false;
    }

    // Check if this is a separator row
    if (TABLE_ROW_DIVIDER_REG_EXP.test(`|${rowText}|`)) {
      // This is a separator row, parse alignments
      const alignmentTexts = rowText.split('|').map(s => s.trim()).filter(Boolean);
      const alignments = alignmentTexts.map(parseAlignment);

      // Store alignments for the next table creation
      (parentNode as any).__tableAlignments = alignments;
      return true; // Consume this line but don't create a node yet
    }

    // Parse cell contents
    const cellTexts = rowText.split('|').map(s => s.trim()).filter(Boolean);

    if (cellTexts.length === 0) {
      return false;
    }

    // Check if we're continuing an existing table
    const previousSibling = parentNode.getPreviousSibling();
    const alignments = (parentNode as any).__tableAlignments || [];

    if ($isTableNode(previousSibling)) {
      // Add row to existing table
      const tableRow = $createTableRowNode();

      cellTexts.forEach((text, index) => {
        const alignment = alignments[index] || null;
        const cell = createTableCellFromText(text, false, alignment);
        tableRow.append(cell);
      });

      previousSibling.append(tableRow);
      parentNode.remove();
      return true;
    } else {
      // Create new table
      const table = $createTableNode();
      const tableRow = $createTableRowNode();

      // Check if this might be a header row (next sibling is separator)
      const nextSibling = parentNode.getNextSibling();
      const isHeaderRow = nextSibling &&
        nextSibling.getTextContent &&
        TABLE_ROW_DIVIDER_REG_EXP.test(nextSibling.getTextContent());

      cellTexts.forEach((text, index) => {
        const alignment = alignments[index] || null;
        const cell = createTableCellFromText(text, isHeaderRow, alignment);
        tableRow.append(cell);
      });

      table.append(tableRow);
      parentNode.replace(table);

      if (!isImport) {
        table.selectEnd();
      }

      return true;
    }
  },
  type: 'element',
};

/**
 * Utility functions for table operations
 */
export const TableUtils = {
  /**
   * Get table dimensions
   */
  getTableDimensions(table: TableNode): { rows: number; cols: number } {
    const rows = table.getChildren().filter($isTableRowNode);
    const cols = rows.length > 0 ?
      rows[0].getChildren().filter($isTableCellNode).length : 0;

    return { rows: rows.length, cols };
  },

  /**
   * Check if table has header row
   */
  hasHeaderRow(table: TableNode): boolean {
    const firstRow = table.getFirstChild();
    if (!$isTableRowNode(firstRow)) {
      return false;
    }

    const firstCell = firstRow.getFirstChild();
    return $isTableCellNode(firstCell) && firstCell.hasHeader();
  },

  /**
   * Convert table to simple format (no alignments)
   */
  toSimpleTable(table: TableNode): string {
    const rows = table.getChildren().filter($isTableRowNode);
    const output: string[] = [];

    rows.forEach(row => {
      const cells = row.getChildren().filter($isTableCellNode);
      const cellTexts = cells.map(cell => extractCellText(cell).replace(/\|/g, '\\|'));
      output.push(`| ${cellTexts.join(' | ')} |`);
    });

    return output.join('\n');
  },

  /**
   * Create table from 2D array of strings
   */
  createTableFromArray(
    data: string[][],
    hasHeader: boolean = false,
    alignments: TableAlignment[] = []
  ): TableNode {
    const table = $createTableNode();

    data.forEach((rowData, rowIndex) => {
      const tableRow = $createTableRowNode();

      rowData.forEach((cellText, cellIndex) => {
        const isHeaderCell = hasHeader && rowIndex === 0;
        const alignment = alignments[cellIndex] || null;
        const cell = createTableCellFromText(cellText, isHeaderCell, alignment);
        tableRow.append(cell);
      });

      table.append(tableRow);
    });

    return table;
  },

  /**
   * Extract table data as 2D array
   */
  extractTableData(table: TableNode): string[][] {
    const rows = table.getChildren().filter($isTableRowNode);
    return rows.map(row => {
      const cells = row.getChildren().filter($isTableCellNode);
      return cells.map(cell => extractCellText(cell));
    });
  },
};
