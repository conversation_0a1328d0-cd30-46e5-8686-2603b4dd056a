/**
 * Obsidian wikilink transformer
 * Handles Obsidian-specific wikilink syntax: [[page]] and [[page|alias]]
 */

import type { TextMatchTransformer } from '@lexical/markdown';
import { $createLinkNode, LinkNode } from '@lexical/link';
import { $createTextNode, LexicalNode, TextNode } from 'lexical';

/**
 * Custom wikilink node that extends LinkNode
 */
export class WikilinkNode extends LinkNode {
  __target: string;
  __alias?: string;

  static getType(): string {
    return 'wikilink';
  }

  constructor(target: string, alias?: string, key?: string) {
    // Use a placeholder URL for wikilinks
    super(`obsidian://open?vault=current&file=${encodeURIComponent(target)}`, {}, key);
    this.__target = target;
    this.__alias = alias;
  }

  getTarget(): string {
    return this.__target;
  }

  setTarget(target: string): void {
    const writable = this.getWritable();
    writable.__target = target;
    writable.__url = `obsidian://open?vault=current&file=${encodeURIComponent(target)}`;
  }

  getAlias(): string | undefined {
    return this.__alias;
  }

  setAlias(alias: string | undefined): void {
    const writable = this.getWritable();
    writable.__alias = alias;
  }

  getDisplayText(): string {
    return this.__alias || this.__target;
  }

  createDOM(): HTMLElement {
    const element = super.createDOM();
    element.classList.add('wikilink');
    element.setAttribute('data-target', this.__target);
    
    if (this.__alias) {
      element.setAttribute('data-alias', this.__alias);
    }

    // Override the href to prevent navigation
    element.setAttribute('href', '#');
    element.addEventListener('click', (e) => {
      e.preventDefault();
      // Emit custom event for wikilink navigation
      const event = new CustomEvent('wikilink-click', {
        detail: { target: this.__target, alias: this.__alias },
      });
      document.dispatchEvent(event);
    });

    return element;
  }

  static clone(node: WikilinkNode): WikilinkNode {
    return new WikilinkNode(node.__target, node.__alias, node.__key);
  }

  static importJSON(serializedNode: any): WikilinkNode {
    const { target, alias } = serializedNode;
    return $createWikilinkNode(target, alias);
  }

  exportJSON(): any {
    return {
      ...super.exportJSON(),
      type: 'wikilink',
      target: this.__target,
      alias: this.__alias,
    };
  }

  canInsertTextBefore(): false {
    return false;
  }

  canInsertTextAfter(): false {
    return false;
  }

  isInline(): true {
    return true;
  }
}

/**
 * Create a wikilink node
 */
export function $createWikilinkNode(target: string, alias?: string): WikilinkNode {
  return new WikilinkNode(target, alias);
}

/**
 * Check if node is a wikilink node
 */
export function $isWikilinkNode(node: LexicalNode | null | undefined): node is WikilinkNode {
  return node instanceof WikilinkNode;
}

/**
 * Obsidian wikilink transformer
 */
export const OBSIDIAN_WIKILINK: TextMatchTransformer = {
  dependencies: [WikilinkNode],
  export: (
    node: LexicalNode,
    exportChildren: (node: any) => string,
    exportFormat: (node: TextNode, textContent: string) => string
  ): string | null => {
    if (!$isWikilinkNode(node)) {
      return null;
    }

    const target = node.getTarget();
    const alias = node.getAlias();
    
    if (alias && alias !== target) {
      return `[[${target}|${alias}]]`;
    } else {
      return `[[${target}]]`;
    }
  },
  importRegExp: /\[\[([^\]|]+)(\|([^\]]+))?\]\]/,
  replace: (textNode: TextNode, match: RegExpMatchArray): void => {
    const target = match[1];
    const alias = match[3];
    
    const wikilinkNode = $createWikilinkNode(target, alias);
    
    // Set the display text
    const displayText = alias || target;
    wikilinkNode.append($createTextNode(displayText));
    
    textNode.replace(wikilinkNode);
  },
  trigger: ']]',
  type: 'text-match',
};

/**
 * Enhanced wikilink transformer that also handles file extensions
 */
export const ENHANCED_WIKILINK: TextMatchTransformer = {
  dependencies: [WikilinkNode],
  export: (
    node: LexicalNode,
    exportChildren: (node: any) => string,
    exportFormat: (node: TextNode, textContent: string) => string
  ): string | null => {
    if (!$isWikilinkNode(node)) {
      return null;
    }

    const target = node.getTarget();
    const alias = node.getAlias();
    
    // Clean target (remove file extension if present)
    const cleanTarget = WikilinkUtils.cleanTarget(target);
    
    if (alias && alias !== cleanTarget) {
      return `[[${cleanTarget}|${alias}]]`;
    } else {
      return `[[${cleanTarget}]]`;
    }
  },
  importRegExp: /\[\[([^\]|#]+)(?:#([^\]|]+))?(\|([^\]]+))?\]\]/,
  replace: (textNode: TextNode, match: RegExpMatchArray): void => {
    const target = match[1];
    const heading = match[2];
    const alias = match[4];
    
    // Construct full target with heading if present
    const fullTarget = heading ? `${target}#${heading}` : target;
    
    const wikilinkNode = $createWikilinkNode(fullTarget, alias);
    
    // Set the display text
    const displayText = alias || (heading ? `${target} > ${heading}` : target);
    wikilinkNode.append($createTextNode(displayText));
    
    textNode.replace(wikilinkNode);
  },
  trigger: ']]',
  type: 'text-match',
};

/**
 * Utility functions for wikilinks
 */
export class WikilinkUtils {
  /**
   * Clean target by removing file extension
   */
  static cleanTarget(target: string): string {
    // Remove common file extensions
    return target.replace(/\.(md|txt|pdf|png|jpg|jpeg|gif|svg)$/i, '');
  }

  /**
   * Extract heading from target
   */
  static extractHeading(target: string): { file: string; heading?: string } {
    const parts = target.split('#');
    return {
      file: parts[0],
      heading: parts[1],
    };
  }

  /**
   * Normalize target for comparison
   */
  static normalizeTarget(target: string): string {
    return target
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
  }

  /**
   * Check if target exists (would be implemented with actual file system check)
   */
  static async targetExists(target: string): Promise<boolean> {
    // This would implement actual file existence check
    // For now, assume all targets exist
    return true;
  }

  /**
   * Get suggestions for partial target
   */
  static getSuggestions(partial: string, availableTargets: string[]): string[] {
    const normalizedPartial = this.normalizeTarget(partial);
    
    return availableTargets
      .filter(target => {
        const normalizedTarget = this.normalizeTarget(target);
        return normalizedTarget.includes(normalizedPartial);
      })
      .sort((a, b) => {
        const aScore = this.getSimilarityScore(partial, a);
        const bScore = this.getSimilarityScore(partial, b);
        return bScore - aScore;
      })
      .slice(0, 10); // Limit to top 10 suggestions
  }

  /**
   * Calculate similarity score between two strings
   */
  private static getSimilarityScore(a: string, b: string): number {
    const normalizedA = this.normalizeTarget(a);
    const normalizedB = this.normalizeTarget(b);
    
    if (normalizedB.startsWith(normalizedA)) {
      return 1.0;
    }
    
    if (normalizedB.includes(normalizedA)) {
      return 0.8;
    }
    
    // Simple character overlap score
    const aChars = new Set(normalizedA);
    const bChars = new Set(normalizedB);
    const intersection = new Set([...aChars].filter(x => bChars.has(x)));
    const union = new Set([...aChars, ...bChars]);
    
    return intersection.size / union.size;
  }

  /**
   * Convert wikilink to regular link
   */
  static toRegularLink(wikilink: WikilinkNode, baseUrl: string = ''): LinkNode {
    const target = wikilink.getTarget();
    const alias = wikilink.getAlias();
    const { file, heading } = this.extractHeading(target);
    
    // Construct URL
    let url = `${baseUrl}/${encodeURIComponent(file)}`;
    if (heading) {
      url += `#${encodeURIComponent(heading)}`;
    }
    
    const linkNode = $createLinkNode(url);
    linkNode.append($createTextNode(alias || target));
    
    return linkNode;
  }

  /**
   * Extract all wikilinks from text
   */
  static extractWikilinks(text: string): Array<{ target: string; alias?: string; match: string }> {
    const regex = /\[\[([^\]|#]+)(?:#([^\]|]+))?(\|([^\]]+))?\]\]/g;
    const wikilinks: Array<{ target: string; alias?: string; match: string }> = [];
    
    let match;
    while ((match = regex.exec(text)) !== null) {
      const target = match[1];
      const heading = match[2];
      const alias = match[4];
      
      const fullTarget = heading ? `${target}#${heading}` : target;
      
      wikilinks.push({
        target: fullTarget,
        alias,
        match: match[0],
      });
    }
    
    return wikilinks;
  }

  /**
   * Replace wikilinks in text with regular links
   */
  static replaceWikilinksInText(text: string, baseUrl: string = ''): string {
    return text.replace(
      /\[\[([^\]|#]+)(?:#([^\]|]+))?(\|([^\]]+))?\]\]/g,
      (match, target, heading, _, alias) => {
        const fullTarget = heading ? `${target}#${heading}` : target;
        const displayText = alias || fullTarget;
        let url = `${baseUrl}/${encodeURIComponent(target)}`;
        if (heading) {
          url += `#${encodeURIComponent(heading)}`;
        }
        return `[${displayText}](${url})`;
      }
    );
  }
}
