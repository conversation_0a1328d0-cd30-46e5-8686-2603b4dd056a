/**
 * Ghost callout transformer
 * Handles Ghost-specific callout syntax: > [!note], > [!warning], etc.
 */

import type { ElementTransformer } from '@lexical/markdown';
import { $createQuoteNode, QuoteNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode, ElementNode, LexicalNode } from 'lexical';

/**
 * Supported Ghost callout types
 */
export const GHOST_CALLOUT_TYPES = [
  'note',
  'info',
  'warning',
  'danger',
  'success',
  'tip',
  'important',
  'caution',
] as const;

export type GhostCalloutType = typeof GHOST_CALLOUT_TYPES[number];

/**
 * Custom callout node that extends QuoteNode
 */
export class CalloutNode extends QuoteNode {
  __calloutType: GhostCalloutType;

  static getType(): string {
    return 'callout';
  }

  constructor(calloutType: GhostCalloutType, key?: string) {
    super(key);
    this.__calloutType = calloutType;
  }

  getCalloutType(): GhostCalloutType {
    return this.__calloutType;
  }

  setCalloutType(calloutType: GhostCalloutType): void {
    const writable = this.getWritable();
    writable.__calloutType = calloutType;
  }

  createDOM(): HTMLElement {
    const element = super.createDOM();
    element.classList.add('callout', `callout-${this.__calloutType}`);
    element.setAttribute('data-callout-type', this.__calloutType);
    return element;
  }

  static clone(node: CalloutNode): CalloutNode {
    return new CalloutNode(node.__calloutType, node.__key);
  }

  static importJSON(serializedNode: any): CalloutNode {
    const { calloutType } = serializedNode;
    const node = $createCalloutNode(calloutType);
    return node;
  }

  exportJSON(): any {
    return {
      ...super.exportJSON(),
      calloutType: this.__calloutType,
      type: 'callout',
    };
  }
}

/**
 * Create a callout node
 */
export function $createCalloutNode(calloutType: GhostCalloutType): CalloutNode {
  return new CalloutNode(calloutType);
}

/**
 * Check if node is a callout node
 */
export function $isCalloutNode(node: LexicalNode | null | undefined): node is CalloutNode {
  return node instanceof CalloutNode;
}

/**
 * Ghost callout transformer
 */
export const GHOST_CALLOUT: ElementTransformer = {
  dependencies: [CalloutNode],
  export: (node: LexicalNode, traverseChildren: (node: ElementNode) => string): string | null => {
    if (!$isCalloutNode(node)) {
      return null;
    }

    const calloutType = node.getCalloutType();
    const content = traverseChildren(node);
    
    // Format as Ghost callout syntax
    const lines = content.split('\n');
    const formattedLines = lines.map((line, index) => {
      if (index === 0) {
        return `> [!${calloutType}]\n> ${line}`;
      }
      return `> ${line}`;
    });

    return formattedLines.join('\n');
  },
  regExp: /^>\s*\[!([^\]]+)\](?:\s+(.*))?$/,
  replace: (
    parentNode: ElementNode,
    children: Array<LexicalNode>,
    match: Array<string>,
    isImport: boolean
  ): boolean | void => {
    const calloutType = match[1].toLowerCase() as GhostCalloutType;
    
    // Validate callout type
    if (!GHOST_CALLOUT_TYPES.includes(calloutType)) {
      // Fall back to regular quote if unknown type
      return false;
    }

    const calloutNode = $createCalloutNode(calloutType);
    
    // If there's content after the callout marker, add it
    if (match[2]) {
      const paragraph = $createParagraphNode();
      paragraph.append($createTextNode(match[2]));
      calloutNode.append(paragraph);
    }
    
    // Add any additional children
    if (children.length > 0) {
      calloutNode.append(...children);
    }

    parentNode.replace(calloutNode);
    
    if (!isImport) {
      calloutNode.select(0, 0);
    }

    return true;
  },
  type: 'element',
};

/**
 * Enhanced quote transformer that handles both regular quotes and callouts
 */
export const ENHANCED_QUOTE: ElementTransformer = {
  dependencies: [QuoteNode, CalloutNode],
  export: (node: LexicalNode, traverseChildren: (node: ElementNode) => string): string | null => {
    // Handle callouts first
    if ($isCalloutNode(node)) {
      return GHOST_CALLOUT.export(node, traverseChildren);
    }

    // Handle regular quotes
    if (node.getType() === 'quote') {
      const content = traverseChildren(node);
      const lines = content.split('\n');
      return lines.map(line => `> ${line}`).join('\n');
    }

    return null;
  },
  regExp: /^>\s*(.*)$/,
  replace: (
    parentNode: ElementNode,
    children: Array<LexicalNode>,
    match: Array<string>,
    isImport: boolean
  ): boolean | void => {
    const content = match[1];
    
    // Check if this is a callout
    const calloutMatch = content.match(/^\[!([^\]]+)\](?:\s+(.*))?$/);
    if (calloutMatch) {
      const calloutType = calloutMatch[1].toLowerCase() as GhostCalloutType;
      
      if (GHOST_CALLOUT_TYPES.includes(calloutType)) {
        const calloutNode = $createCalloutNode(calloutType);
        
        if (calloutMatch[2]) {
          const paragraph = $createParagraphNode();
          paragraph.append($createTextNode(calloutMatch[2]));
          calloutNode.append(paragraph);
        }
        
        if (children.length > 0) {
          calloutNode.append(...children);
        }

        parentNode.replace(calloutNode);
        
        if (!isImport) {
          calloutNode.select(0, 0);
        }

        return true;
      }
    }

    // Regular quote
    const quoteNode = $createQuoteNode();
    
    if (content) {
      const paragraph = $createParagraphNode();
      paragraph.append($createTextNode(content));
      quoteNode.append(paragraph);
    }
    
    if (children.length > 0) {
      quoteNode.append(...children);
    }

    parentNode.replace(quoteNode);
    
    if (!isImport) {
      quoteNode.select(0, 0);
    }

    return true;
  },
  type: 'element',
};

/**
 * Utility functions for working with callouts
 */
export class CalloutUtils {
  /**
   * Check if a callout type is valid
   */
  static isValidCalloutType(type: string): type is GhostCalloutType {
    return GHOST_CALLOUT_TYPES.includes(type as GhostCalloutType);
  }

  /**
   * Get callout type from markdown line
   */
  static extractCalloutType(line: string): GhostCalloutType | null {
    const match = line.match(/^>\s*\[!([^\]]+)\]/);
    if (match) {
      const type = match[1].toLowerCase();
      return this.isValidCalloutType(type) ? type : null;
    }
    return null;
  }

  /**
   * Convert callout type to CSS class
   */
  static getCalloutClass(type: GhostCalloutType): string {
    return `callout-${type}`;
  }

  /**
   * Get callout icon (for UI purposes)
   */
  static getCalloutIcon(type: GhostCalloutType): string {
    const icons: Record<GhostCalloutType, string> = {
      note: '📝',
      info: 'ℹ️',
      warning: '⚠️',
      danger: '🚨',
      success: '✅',
      tip: '💡',
      important: '❗',
      caution: '⚠️',
    };
    return icons[type] || '📝';
  }

  /**
   * Get callout color theme (for UI purposes)
   */
  static getCalloutTheme(type: GhostCalloutType): { bg: string; border: string; text: string } {
    const themes: Record<GhostCalloutType, { bg: string; border: string; text: string }> = {
      note: { bg: '#f8f9fa', border: '#6c757d', text: '#495057' },
      info: { bg: '#e7f3ff', border: '#0066cc', text: '#004499' },
      warning: { bg: '#fff3cd', border: '#ffc107', text: '#856404' },
      danger: { bg: '#f8d7da', border: '#dc3545', text: '#721c24' },
      success: { bg: '#d1edff', border: '#28a745', text: '#155724' },
      tip: { bg: '#fff3e0', border: '#ff9800', text: '#e65100' },
      important: { bg: '#e8f4fd', border: '#17a2b8', text: '#0c5460' },
      caution: { bg: '#fff3cd', border: '#ffc107', text: '#856404' },
    };
    return themes[type] || themes.note;
  }
}
