/**
 * Ghost bookmark transformer
 * Handles Ghost-specific bookmark card syntax
 */

import type { ElementTransformer } from '@lexical/markdown';
import { ElementNode, LexicalNode, DecoratorNode } from 'lexical';

/**
 * Bookmark metadata interface
 */
export interface BookmarkMetadata {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  author?: string;
  publisher?: string;
  publishedTime?: string;
  modifiedTime?: string;
  tags?: string[];
}

/**
 * Custom bookmark node for Ghost bookmark cards
 */
export class BookmarkNode extends DecoratorNode<JSX.Element> {
  __url: string;
  __metadata: BookmarkMetadata;

  static getType(): string {
    return 'bookmark';
  }

  constructor(url: string, metadata: Partial<BookmarkMetadata> = {}, key?: string) {
    super(key);
    this.__url = url;
    this.__metadata = {
      url,
      ...metadata,
    };
  }

  getUrl(): string {
    return this.__url;
  }

  setUrl(url: string): void {
    const writable = this.getWritable();
    writable.__url = url;
    writable.__metadata.url = url;
  }

  getMetadata(): BookmarkMetadata {
    return this.__metadata;
  }

  setMetadata(metadata: Partial<BookmarkMetadata>): void {
    const writable = this.getWritable();
    writable.__metadata = {
      ...writable.__metadata,
      ...metadata,
    };
  }

  getTitle(): string | undefined {
    return this.__metadata.title;
  }

  setTitle(title: string): void {
    const writable = this.getWritable();
    writable.__metadata.title = title;
  }

  getDescription(): string | undefined {
    return this.__metadata.description;
  }

  setDescription(description: string): void {
    const writable = this.getWritable();
    writable.__metadata.description = description;
  }

  createDOM(): HTMLElement {
    const element = document.createElement('div');
    element.classList.add('bookmark-card');
    element.setAttribute('data-url', this.__url);
    
    // Create bookmark card structure
    const link = document.createElement('a');
    link.href = this.__url;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    link.classList.add('bookmark-link');

    const content = document.createElement('div');
    content.classList.add('bookmark-content');

    if (this.__metadata.title) {
      const title = document.createElement('h3');
      title.classList.add('bookmark-title');
      title.textContent = this.__metadata.title;
      content.appendChild(title);
    }

    if (this.__metadata.description) {
      const description = document.createElement('p');
      description.classList.add('bookmark-description');
      description.textContent = this.__metadata.description;
      content.appendChild(description);
    }

    const meta = document.createElement('div');
    meta.classList.add('bookmark-meta');
    
    if (this.__metadata.author || this.__metadata.publisher) {
      const author = document.createElement('span');
      author.classList.add('bookmark-author');
      author.textContent = this.__metadata.author || this.__metadata.publisher || '';
      meta.appendChild(author);
    }

    const urlSpan = document.createElement('span');
    urlSpan.classList.add('bookmark-url');
    urlSpan.textContent = this.__url;
    meta.appendChild(urlSpan);

    content.appendChild(meta);
    link.appendChild(content);

    if (this.__metadata.image) {
      const imageContainer = document.createElement('div');
      imageContainer.classList.add('bookmark-image');
      const image = document.createElement('img');
      image.src = this.__metadata.image;
      image.alt = this.__metadata.title || 'Bookmark image';
      imageContainer.appendChild(image);
      link.appendChild(imageContainer);
    }

    element.appendChild(link);
    return element;
  }

  updateDOM(): false {
    return false;
  }

  decorate(): JSX.Element {
    // This would return a React component in a React environment
    // For now, we'll return a placeholder
    return null as any;
  }

  static clone(node: BookmarkNode): BookmarkNode {
    return new BookmarkNode(node.__url, node.__metadata, node.__key);
  }

  static importJSON(serializedNode: any): BookmarkNode {
    const { url, metadata } = serializedNode;
    return $createBookmarkNode(url, metadata);
  }

  exportJSON(): any {
    return {
      type: 'bookmark',
      url: this.__url,
      metadata: this.__metadata,
      version: 1,
    };
  }

  isInline(): false {
    return false;
  }

  isKeyboardSelectable(): true {
    return true;
  }
}

/**
 * Create a bookmark node
 */
export function $createBookmarkNode(url: string, metadata: Partial<BookmarkMetadata> = {}): BookmarkNode {
  return new BookmarkNode(url, metadata);
}

/**
 * Check if node is a bookmark node
 */
export function $isBookmarkNode(node: LexicalNode | null | undefined): node is BookmarkNode {
  return node instanceof BookmarkNode;
}

/**
 * Ghost bookmark transformer
 */
export const GHOST_BOOKMARK: ElementTransformer = {
  dependencies: [BookmarkNode],
  export: (node: LexicalNode): string | null => {
    if (!$isBookmarkNode(node)) {
      return null;
    }

    const url = node.getUrl();
    const metadata = node.getMetadata();
    
    // Simple bookmark format
    if (!metadata.title && !metadata.description) {
      return `[bookmark](${url})`;
    }

    // Extended bookmark format with metadata
    const parts = [`[bookmark](${url})`];
    
    if (metadata.title) {
      parts.push(`title: ${metadata.title}`);
    }
    
    if (metadata.description) {
      parts.push(`description: ${metadata.description}`);
    }
    
    if (metadata.image) {
      parts.push(`image: ${metadata.image}`);
    }

    return parts.join('\n');
  },
  regExp: /^\[bookmark\]\(([^)]+)\)(?:\s*(.*))?$/,
  replace: (
    parentNode: ElementNode,
    children: Array<LexicalNode>,
    match: Array<string>,
    isImport: boolean
  ): boolean | void => {
    const url = match[1];
    const metadataText = match[2];
    
    // Validate URL
    if (!url || !isValidUrl(url)) {
      return false;
    }

    const metadata: Partial<BookmarkMetadata> = {};
    
    // Parse metadata if present
    if (metadataText) {
      const lines = metadataText.split('\n');
      for (const line of lines) {
        const [key, ...valueParts] = line.split(':');
        if (key && valueParts.length > 0) {
          const value = valueParts.join(':').trim();
          switch (key.trim().toLowerCase()) {
            case 'title':
              metadata.title = value;
              break;
            case 'description':
              metadata.description = value;
              break;
            case 'image':
              metadata.image = value;
              break;
            case 'author':
              metadata.author = value;
              break;
            case 'publisher':
              metadata.publisher = value;
              break;
          }
        }
      }
    }

    const bookmarkNode = $createBookmarkNode(url, metadata);
    parentNode.replace(bookmarkNode);
    
    if (!isImport) {
      bookmarkNode.select();
    }

    return true;
  },
  type: 'element',
};

/**
 * Utility functions for bookmarks
 */
export class BookmarkUtils {
  /**
   * Validate URL format
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Extract domain from URL
   */
  static extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return '';
    }
  }

  /**
   * Fetch bookmark metadata (would be implemented with actual HTTP requests)
   */
  static async fetchMetadata(url: string): Promise<BookmarkMetadata> {
    // This would implement actual metadata fetching
    // For now, return basic metadata
    return {
      url,
      title: this.extractDomain(url),
      description: `Bookmark for ${url}`,
    };
  }

  /**
   * Create bookmark from URL with metadata fetching
   */
  static async createBookmarkWithMetadata(url: string): Promise<BookmarkNode> {
    const metadata = await this.fetchMetadata(url);
    return $createBookmarkNode(url, metadata);
  }

  /**
   * Extract URLs from text
   */
  static extractUrls(text: string): string[] {
    const urlRegex = /https?:\/\/[^\s]+/g;
    return text.match(urlRegex) || [];
  }

  /**
   * Convert bookmark to Ghost card format
   */
  static toGhostCard(bookmark: BookmarkNode): any {
    const metadata = bookmark.getMetadata();
    return {
      type: 'bookmark',
      url: bookmark.getUrl(),
      metadata: {
        url: bookmark.getUrl(),
        title: metadata.title,
        description: metadata.description,
        author: metadata.author,
        publisher: metadata.publisher,
        thumbnail: metadata.image,
        icon: `https://www.google.com/s2/favicons?domain=${this.extractDomain(bookmark.getUrl())}`,
      },
    };
  }

  /**
   * Create bookmark from Ghost card
   */
  static fromGhostCard(card: any): BookmarkNode {
    const metadata: Partial<BookmarkMetadata> = {
      title: card.metadata?.title,
      description: card.metadata?.description,
      author: card.metadata?.author,
      publisher: card.metadata?.publisher,
      image: card.metadata?.thumbnail,
    };

    return $createBookmarkNode(card.url, metadata);
  }
}

/**
 * Helper function to validate URL (used in transformer)
 */
function isValidUrl(url: string): boolean {
  return BookmarkUtils.isValidUrl(url);
}
