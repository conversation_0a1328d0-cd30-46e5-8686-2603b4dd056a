/**
 * JSON schema validation for parser configuration and transformer definitions
 * Implements validation based on the JSON specification
 */

import type {
  ParserConfig,
  TransformerConfig,
  Extension,
  ConversionOptions,
  PerformanceConfig,
} from './parser-config';
import type { ConversionResult } from './conversion-types';
import { ConversionResultBuilder } from './conversion-types';

/**
 * Validation schema definitions
 */
interface ValidationSchema {
  type: string;
  properties?: Record<string, ValidationSchema>;
  required?: string[];
  items?: ValidationSchema;
  enum?: any[];
  minimum?: number;
  maximum?: number;
  pattern?: string;
  additionalProperties?: boolean;
}

/**
 * Configuration validator class
 */
export class ConfigValidator {
  private static schemas: Record<string, ValidationSchema> = {
    ParserConfig: {
      type: 'object',
      properties: {
        transformers: {
          type: 'array',
          items: { type: 'TransformerConfig' },
        },
        options: { type: 'ConversionOptions' },
        extensions: {
          type: 'array',
          items: { type: 'Extension' },
        },
      },
      required: ['transformers'],
    },
    
    TransformerConfig: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: ['element', 'multilineElement', 'textFormat', 'textMatch'],
        },
        nodeType: { type: 'string' },
        priority: { type: 'number' },
        regExp: { type: 'string' },
        dependencies: {
          type: 'array',
          items: { type: 'string' },
        },
        export: { type: 'ExportFunction' },
        import: { type: 'ImportFunction' },
      },
      required: ['type', 'nodeType'],
    },
    
    Extension: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        platform: {
          type: 'string',
          enum: ['ghost', 'obsidian', 'both'],
        },
        transformers: {
          type: 'array',
          items: { type: 'TransformerConfig' },
        },
        enabled: { type: 'boolean' },
      },
      required: ['name', 'platform', 'transformers'],
    },
    
    ConversionOptions: {
      type: 'object',
      properties: {
        preserveUnknownNodes: { type: 'boolean' },
        enableGhostFeatures: { type: 'boolean' },
        enableObsidianFeatures: { type: 'boolean' },
        fallbackToHTML: { type: 'boolean' },
        shouldPreserveNewLines: { type: 'boolean' },
        shouldMergeAdjacentLines: { type: 'boolean' },
        performance: { type: 'PerformanceConfig' },
      },
    },
    
    PerformanceConfig: {
      type: 'object',
      properties: {
        batchSize: {
          type: 'number',
          minimum: 1,
          maximum: 10000,
        },
        useStringBuilder: { type: 'boolean' },
        largeDocumentThreshold: {
          type: 'number',
          minimum: 1000,
        },
        enableConverterCache: { type: 'boolean' },
      },
    },
    
    ExportFunction: {
      type: 'object',
      properties: {
        handler: { type: 'string' },
        options: { type: 'object' },
      },
      required: ['handler'],
    },
    
    ImportFunction: {
      type: 'object',
      properties: {
        handler: { type: 'string' },
        options: { type: 'object' },
      },
      required: ['handler'],
    },
  };

  /**
   * Validate parser configuration
   */
  static validateParserConfig(config: any): ConversionResult<ParserConfig> {
    const builder = new ConversionResultBuilder();
    
    try {
      // Basic type check
      if (!config || typeof config !== 'object') {
        return builder.error('INVALID_INPUT', 'Configuration must be an object');
      }

      // Validate against schema
      const validation = this.validateAgainstSchema(config, 'ParserConfig');
      if (!validation.isValid) {
        return builder.error('INVALID_INPUT', 'Configuration validation failed', {
          errors: validation.errors,
        });
      }

      // Additional business logic validation
      const businessValidation = this.validateBusinessLogic(config);
      if (!businessValidation.success) {
        return businessValidation;
      }

      return builder.success(config as ParserConfig);
    } catch (error) {
      return builder.error(
        'UNEXPECTED_ERROR',
        error instanceof Error ? error.message : 'Validation error',
        undefined,
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Validate transformer configuration
   */
  static validateTransformerConfig(config: any): ConversionResult<TransformerConfig> {
    const builder = new ConversionResultBuilder();
    
    try {
      const validation = this.validateAgainstSchema(config, 'TransformerConfig');
      if (!validation.isValid) {
        return builder.error('INVALID_INPUT', 'Transformer validation failed', {
          errors: validation.errors,
        });
      }

      // Validate regex if present
      if (config.regExp) {
        try {
          new RegExp(config.regExp);
        } catch (regexError) {
          return builder.error('INVALID_INPUT', 'Invalid regular expression', {
            regExp: config.regExp,
            error: regexError instanceof Error ? regexError.message : 'Unknown regex error',
          });
        }
      }

      return builder.success(config as TransformerConfig);
    } catch (error) {
      return builder.error(
        'UNEXPECTED_ERROR',
        error instanceof Error ? error.message : 'Validation error',
        undefined,
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Validate extension configuration
   */
  static validateExtension(config: any): ConversionResult<Extension> {
    const builder = new ConversionResultBuilder();
    
    try {
      const validation = this.validateAgainstSchema(config, 'Extension');
      if (!validation.isValid) {
        return builder.error('INVALID_INPUT', 'Extension validation failed', {
          errors: validation.errors,
        });
      }

      // Validate each transformer in the extension
      for (let i = 0; i < config.transformers.length; i++) {
        const transformerValidation = this.validateTransformerConfig(config.transformers[i]);
        if (!transformerValidation.success) {
          return builder.error('INVALID_INPUT', `Invalid transformer at index ${i}`, {
            transformerErrors: transformerValidation.errors,
          });
        }
      }

      return builder.success(config as Extension);
    } catch (error) {
      return builder.error(
        'UNEXPECTED_ERROR',
        error instanceof Error ? error.message : 'Validation error',
        undefined,
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Validate JSON string configuration
   */
  static validateJSONConfig(jsonString: string): ConversionResult<ParserConfig> {
    const builder = new ConversionResultBuilder();
    
    try {
      const config = JSON.parse(jsonString);
      return this.validateParserConfig(config);
    } catch (parseError) {
      return builder.error(
        'PARSE_FAILED',
        parseError instanceof Error ? parseError.message : 'Failed to parse JSON',
        undefined,
        parseError instanceof Error ? parseError.stack : undefined
      );
    }
  }

  /**
   * Validate against schema
   */
  private static validateAgainstSchema(
    data: any,
    schemaName: string,
    path: string = ''
  ): { isValid: boolean; errors: string[] } {
    const schema = this.schemas[schemaName];
    if (!schema) {
      return { isValid: false, errors: [`Unknown schema: ${schemaName}`] };
    }

    const errors: string[] = [];

    // Type validation
    if (!this.validateType(data, schema.type, path)) {
      errors.push(`${path || 'root'}: Expected ${schema.type}, got ${typeof data}`);
      return { isValid: false, errors };
    }

    // Object property validation
    if (schema.type === 'object' && schema.properties) {
      for (const [key, propSchema] of Object.entries(schema.properties)) {
        const propPath = path ? `${path}.${key}` : key;
        
        if (data[key] !== undefined) {
          if (typeof propSchema === 'object' && propSchema.type) {
            if (this.schemas[propSchema.type]) {
              // Nested schema validation
              const nestedValidation = this.validateAgainstSchema(data[key], propSchema.type, propPath);
              errors.push(...nestedValidation.errors);
            } else {
              // Direct type validation
              if (!this.validateType(data[key], propSchema.type, propPath)) {
                errors.push(`${propPath}: Expected ${propSchema.type}, got ${typeof data[key]}`);
              }
              
              // Additional validations
              if (propSchema.enum && !propSchema.enum.includes(data[key])) {
                errors.push(`${propPath}: Value must be one of: ${propSchema.enum.join(', ')}`);
              }
              
              if (propSchema.minimum !== undefined && data[key] < propSchema.minimum) {
                errors.push(`${propPath}: Value must be >= ${propSchema.minimum}`);
              }
              
              if (propSchema.maximum !== undefined && data[key] > propSchema.maximum) {
                errors.push(`${propPath}: Value must be <= ${propSchema.maximum}`);
              }
              
              if (propSchema.pattern && typeof data[key] === 'string') {
                const regex = new RegExp(propSchema.pattern);
                if (!regex.test(data[key])) {
                  errors.push(`${propPath}: Value does not match pattern ${propSchema.pattern}`);
                }
              }
            }
          }
        }
      }
      
      // Required property validation
      if (schema.required) {
        for (const requiredProp of schema.required) {
          if (data[requiredProp] === undefined) {
            errors.push(`${path || 'root'}: Missing required property '${requiredProp}'`);
          }
        }
      }
    }

    // Array validation
    if (schema.type === 'array' && schema.items && Array.isArray(data)) {
      for (let i = 0; i < data.length; i++) {
        const itemPath = `${path}[${i}]`;
        if (this.schemas[schema.items.type]) {
          const itemValidation = this.validateAgainstSchema(data[i], schema.items.type, itemPath);
          errors.push(...itemValidation.errors);
        }
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Validate data type
   */
  private static validateType(data: any, expectedType: string, path: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof data === 'string';
      case 'number':
        return typeof data === 'number' && !isNaN(data);
      case 'boolean':
        return typeof data === 'boolean';
      case 'object':
        return data !== null && typeof data === 'object' && !Array.isArray(data);
      case 'array':
        return Array.isArray(data);
      default:
        // Custom type - assume valid for now
        return true;
    }
  }

  /**
   * Validate business logic rules
   */
  private static validateBusinessLogic(config: ParserConfig): ConversionResult<ParserConfig> {
    const builder = new ConversionResultBuilder();

    // Check for duplicate transformer node types
    const nodeTypes = new Set<string>();
    for (const transformer of config.transformers) {
      if (nodeTypes.has(transformer.nodeType)) {
        builder.addWarning(`Duplicate transformer for node type: ${transformer.nodeType}`);
      }
      nodeTypes.add(transformer.nodeType);
    }

    // Validate extension dependencies
    if (config.extensions) {
      for (const extension of config.extensions) {
        // Check for conflicting transformers between extensions
        for (const transformer of extension.transformers) {
          if (nodeTypes.has(transformer.nodeType)) {
            builder.addWarning(
              `Extension '${extension.name}' transformer conflicts with existing transformer for: ${transformer.nodeType}`
            );
          }
        }
      }
    }

    // Validate performance settings
    if (config.options?.performance) {
      const perf = config.options.performance;
      if (perf.batchSize && perf.batchSize > perf.largeDocumentThreshold) {
        builder.addWarning('Batch size is larger than large document threshold');
      }
    }

    return builder.success(config);
  }

  /**
   * Get validation schema for a type
   */
  static getSchema(schemaName: string): ValidationSchema | null {
    return this.schemas[schemaName] || null;
  }

  /**
   * Add custom validation schema
   */
  static addSchema(name: string, schema: ValidationSchema): void {
    this.schemas[name] = schema;
  }
}
