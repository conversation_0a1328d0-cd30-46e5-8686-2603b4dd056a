/**
 * Parser configuration system based on JSON specification
 * Supports both official Lexical transformers and custom extensions
 */

import type { Transformer } from '@lexical/markdown';
import type { NodeConverter } from '../types';

/**
 * Performance configuration for parser optimization
 */
export interface PerformanceConfig {
  batchSize: number;
  useStringBuilder: boolean;
  largeDocumentThreshold: number;
  enableConverterCache: boolean;
}

/**
 * Conversion options for parser behavior
 */
export interface ConversionOptions {
  preserveUnknownNodes?: boolean;
  enableGhostFeatures?: boolean;
  enableObsidianFeatures?: boolean;
  fallbackToHTML?: boolean;
  shouldPreserveNewLines?: boolean;
  shouldMergeAdjacentLines?: boolean;
  performance?: PerformanceConfig;
}

/**
 * Export function configuration
 */
export interface ExportFunction {
  handler: string;
  options?: Record<string, any>;
}

/**
 * Import function configuration
 */
export interface ImportFunction {
  handler: string;
  options?: Record<string, any>;
}

/**
 * Transformer configuration from JSON specification
 */
export interface TransformerConfig {
  type: 'element' | 'multilineElement' | 'textFormat' | 'textMatch';
  nodeType: string;
  priority?: number;
  regExp?: string;
  dependencies?: string[];
  export?: ExportFunction;
  import?: ImportFunction;
}

/**
 * Extension configuration for platform-specific features
 */
export interface Extension {
  name: string;
  platform: 'ghost' | 'obsidian' | 'both';
  transformers: TransformerConfig[];
  enabled?: boolean;
}

/**
 * Main parser configuration
 */
export interface ParserConfig {
  transformers: TransformerConfig[];
  options?: ConversionOptions;
  extensions?: Extension[];
}

/**
 * Conversion error types
 */
export type ConversionErrorCode = 
  | 'NULL_INPUT'
  | 'INVALID_INPUT'
  | 'DOCUMENT_TOO_LARGE'
  | 'PARSE_FAILED'
  | 'CONVERSION_FAILED'
  | 'TRANSFORMER_ERROR'
  | 'UNEXPECTED_ERROR';

/**
 * Conversion error details
 */
export interface ConversionError {
  code: ConversionErrorCode;
  message: string;
  details?: Record<string, any>;
  stack?: string;
}

/**
 * Conversion metadata
 */
export interface ConversionMetadata {
  processingTime?: number;
  nodeCount?: number;
  transformersUsed?: string[];
  fallbacksUsed?: number;
}

/**
 * Generic conversion result
 */
export interface ConversionResult<T> {
  success: boolean;
  data?: T;
  warnings?: string[];
  errors?: ConversionError[];
  metadata?: ConversionMetadata;
}

/**
 * Default performance configuration
 */
export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  batchSize: 1000,
  useStringBuilder: true,
  largeDocumentThreshold: 50000,
  enableConverterCache: true,
};

/**
 * Default conversion options
 */
export const DEFAULT_CONVERSION_OPTIONS: ConversionOptions = {
  preserveUnknownNodes: true,
  enableGhostFeatures: true,
  enableObsidianFeatures: true,
  fallbackToHTML: true,
  shouldPreserveNewLines: false,
  shouldMergeAdjacentLines: false,
  performance: DEFAULT_PERFORMANCE_CONFIG,
};

/**
 * Parser configuration manager
 */
export class ParserConfigManager {
  private static instance: ParserConfigManager;
  private config: ParserConfig;
  private options: ConversionOptions;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.options = { ...DEFAULT_CONVERSION_OPTIONS };
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ParserConfigManager {
    if (!this.instance) {
      this.instance = new ParserConfigManager();
    }
    return this.instance;
  }

  /**
   * Configure parser with new settings
   */
  configure(config: Partial<ParserConfig>): void {
    this.config = {
      ...this.config,
      ...config,
      transformers: config.transformers || this.config.transformers,
      extensions: config.extensions || this.config.extensions,
    };

    if (config.options) {
      this.options = {
        ...this.options,
        ...config.options,
        performance: {
          ...this.options.performance,
          ...config.options.performance,
        },
      };
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): ParserConfig {
    return { ...this.config };
  }

  /**
   * Get current options
   */
  getOptions(): ConversionOptions {
    return { ...this.options };
  }

  /**
   * Get transformers for specific platform
   */
  getTransformersForPlatform(platform: 'ghost' | 'obsidian' | 'both'): TransformerConfig[] {
    const transformers = [...this.config.transformers];

    // Add extension transformers
    if (this.config.extensions) {
      for (const extension of this.config.extensions) {
        if (extension.enabled !== false && 
            (extension.platform === platform || extension.platform === 'both')) {
          transformers.push(...extension.transformers);
        }
      }
    }

    // Sort by priority (higher priority first)
    return transformers.sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }

  /**
   * Validate configuration against JSON schema
   */
  validateConfig(config: ParserConfig): ConversionResult<boolean> {
    try {
      // Basic validation
      if (!config.transformers || !Array.isArray(config.transformers)) {
        return {
          success: false,
          errors: [{
            code: 'INVALID_INPUT',
            message: 'Transformers array is required',
          }],
        };
      }

      // Validate transformers
      for (const transformer of config.transformers) {
        const validation = this.validateTransformer(transformer);
        if (!validation.success) {
          return validation;
        }
      }

      // Validate extensions
      if (config.extensions) {
        for (const extension of config.extensions) {
          const validation = this.validateExtension(extension);
          if (!validation.success) {
            return validation;
          }
        }
      }

      return { success: true, data: true };
    } catch (error) {
      return {
        success: false,
        errors: [{
          code: 'UNEXPECTED_ERROR',
          message: error instanceof Error ? error.message : 'Unknown validation error',
          stack: error instanceof Error ? error.stack : undefined,
        }],
      };
    }
  }

  /**
   * Create error result
   */
  createErrorResult<T>(
    message: string,
    code: ConversionErrorCode,
    details?: Record<string, any>,
    stack?: string
  ): ConversionResult<T> {
    return {
      success: false,
      errors: [{
        code,
        message,
        details,
        stack,
      }],
    };
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): ParserConfig {
    return {
      transformers: [
        // Standard transformers will be populated by the registry
      ],
      options: DEFAULT_CONVERSION_OPTIONS,
      extensions: [
        {
          name: 'ghost-features',
          platform: 'ghost',
          transformers: [],
          enabled: true,
        },
        {
          name: 'obsidian-features',
          platform: 'obsidian',
          transformers: [],
          enabled: true,
        },
      ],
    };
  }

  /**
   * Validate transformer configuration
   */
  private validateTransformer(transformer: TransformerConfig): ConversionResult<boolean> {
    if (!transformer.type || !transformer.nodeType) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: 'Transformer must have type and nodeType',
        }],
      };
    }

    const validTypes = ['element', 'multilineElement', 'textFormat', 'textMatch'];
    if (!validTypes.includes(transformer.type)) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: `Invalid transformer type: ${transformer.type}`,
        }],
      };
    }

    return { success: true, data: true };
  }

  /**
   * Validate extension configuration
   */
  private validateExtension(extension: Extension): ConversionResult<boolean> {
    if (!extension.name || !extension.platform || !extension.transformers) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: 'Extension must have name, platform, and transformers',
        }],
      };
    }

    const validPlatforms = ['ghost', 'obsidian', 'both'];
    if (!validPlatforms.includes(extension.platform)) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: `Invalid platform: ${extension.platform}`,
        }],
      };
    }

    // Validate extension transformers
    for (const transformer of extension.transformers) {
      const validation = this.validateTransformer(transformer);
      if (!validation.success) {
        return validation;
      }
    }

    return { success: true, data: true };
  }
}

/**
 * Utility functions for configuration management
 */
export class ConfigUtils {
  /**
   * Load configuration from JSON
   */
  static loadFromJSON(json: string): ConversionResult<ParserConfig> {
    try {
      const config = JSON.parse(json) as ParserConfig;
      const manager = ParserConfigManager.getInstance();
      const validation = manager.validateConfig(config);
      
      if (!validation.success) {
        return validation as ConversionResult<ParserConfig>;
      }

      return { success: true, data: config };
    } catch (error) {
      return {
        success: false,
        errors: [{
          code: 'PARSE_FAILED',
          message: error instanceof Error ? error.message : 'Failed to parse JSON',
          stack: error instanceof Error ? error.stack : undefined,
        }],
      };
    }
  }

  /**
   * Save configuration to JSON
   */
  static saveToJSON(config: ParserConfig): string {
    return JSON.stringify(config, null, 2);
  }

  /**
   * Merge configurations
   */
  static mergeConfigs(base: ParserConfig, override: Partial<ParserConfig>): ParserConfig {
    return {
      transformers: override.transformers || base.transformers,
      options: {
        ...base.options,
        ...override.options,
        performance: {
          ...base.options?.performance,
          ...override.options?.performance,
        },
      },
      extensions: override.extensions || base.extensions,
    };
  }
}
