/**
 * Main hybrid parser that combines official Lexical transformers with custom extensions
 * Provides backward compatibility with existing API while leveraging official package
 */

import type { Transformer } from '@lexical/markdown';
import {
  HEADING,
  QUOTE,
  UNORDERED_LIST,
  ORDERED_LIST,
  CODE,
  INLINE_CODE,
  BOLD_STAR,
  BOLD_UNDERSCORE,
  ITALIC_STAR,
  ITALIC_UNDERSCORE,
  STRIKETHROUGH,
  LINK,
  TRANSFORMERS as OFFICIAL_TRANSFORMERS,
} from '@lexical/markdown';

import type { LexicalDocument } from '../types';
import type {
  ParserConfig,
  ConversionOptions,
  DEFAULT_CONVERSION_OPTIONS,
} from './parser-config';
import type {
  MarkdownToLexicalResult,
  LexicalToMarkdownResult,
} from './conversion-types';
import { ConversionResultBuilder } from './conversion-types';
import { HybridTransformerRegistry } from './transformer-registry';
import { LexicalAdapter } from './lexical-adapter';
import { ParserConfigManager } from './parser-config';
import { ConfigValidator } from './config-validator';

/**
 * Main hybrid parser class that provides the unified API
 */
export class HybridLexicalParser {
  private static instance: HybridLexicalParser;
  private registry: HybridTransformerRegistry;
  private configManager: ParserConfigManager;
  private initialized = false;

  private constructor() {
    this.registry = HybridTransformerRegistry.getInstance();
    this.configManager = ParserConfigManager.getInstance();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): HybridLexicalParser {
    if (!this.instance) {
      this.instance = new HybridLexicalParser();
    }
    return this.instance;
  }

  /**
   * Initialize parser with configuration
   */
  async initialize(config?: Partial<ParserConfig>): Promise<void> {
    if (this.initialized) {
      return;
    }

    // Apply configuration if provided
    if (config) {
      const validation = ConfigValidator.validateParserConfig(config);
      if (!validation.success) {
        throw new Error(`Invalid configuration: ${validation.errors?.[0]?.message}`);
      }
      this.configManager.configure(config);
    }

    // Register default transformers
    await this.registerDefaultTransformers();

    this.initialized = true;
  }

  /**
   * Convert markdown to Lexical document (main API)
   */
  static async markdownToLexical(
    markdown: string,
    options?: ConversionOptions
  ): Promise<MarkdownToLexicalResult> {
    const instance = this.getInstance();
    await instance.initialize();
    return instance.convertFromMarkdown(markdown, options);
  }

  /**
   * Convert Lexical document to markdown (main API)
   */
  static async lexicalToMarkdown(
    document: LexicalDocument,
    options?: ConversionOptions
  ): Promise<LexicalToMarkdownResult> {
    const instance = this.getInstance();
    await instance.initialize();
    return instance.convertToMarkdown(document, options);
  }

  /**
   * Configure parser
   */
  static configure(config: Partial<ParserConfig>): void {
    const instance = this.getInstance();
    instance.configManager.configure(config);
    instance.initialized = false; // Force re-initialization
  }

  /**
   * Get current configuration
   */
  static getConfig(): ParserConfig {
    const instance = this.getInstance();
    return instance.configManager.getConfig();
  }

  /**
   * Instance method: Convert markdown to Lexical
   */
  private async convertFromMarkdown(
    markdown: string,
    options?: ConversionOptions
  ): Promise<MarkdownToLexicalResult> {
    const builder = new ConversionResultBuilder();

    try {
      // Merge options with defaults
      const mergedOptions = {
        ...DEFAULT_CONVERSION_OPTIONS,
        ...this.configManager.getOptions(),
        ...options,
      };

      // Input validation
      if (!markdown || typeof markdown !== 'string') {
        return builder.error('INVALID_INPUT', 'Markdown input must be a non-empty string');
      }

      if (markdown.length > (mergedOptions.performance?.largeDocumentThreshold || 50000)) {
        builder.addWarning('Large document detected, performance may be affected');
      }

      // Get transformers for conversion
      const transformers = this.getTransformersForOptions(mergedOptions);
      builder.setMetadata({
        transformersUsed: transformers.map(t => this.getTransformerId(t)),
      });

      // Perform conversion using adapter
      const result = await LexicalAdapter.convertFromMarkdown(
        markdown,
        transformers,
        mergedOptions
      );

      if (!result.success) {
        return result;
      }

      // Add our metadata to the result
      const finalResult = builder.success(result.data!);
      if (result.metadata) {
        finalResult.metadata = {
          ...finalResult.metadata,
          ...result.metadata,
        };
      }

      return finalResult;

    } catch (error) {
      return builder.error(
        'UNEXPECTED_ERROR',
        error instanceof Error ? error.message : 'Unknown error occurred',
        { originalError: error },
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Instance method: Convert Lexical to markdown
   */
  private async convertToMarkdown(
    document: LexicalDocument,
    options?: ConversionOptions
  ): Promise<LexicalToMarkdownResult> {
    const builder = new ConversionResultBuilder();

    try {
      // Merge options with defaults
      const mergedOptions = {
        ...DEFAULT_CONVERSION_OPTIONS,
        ...this.configManager.getOptions(),
        ...options,
      };

      // Input validation
      if (!document?.root) {
        return builder.error('INVALID_INPUT', 'Document must have a root node');
      }

      // Get transformers for conversion
      const transformers = this.getTransformersForOptions(mergedOptions);
      builder.setMetadata({
        transformersUsed: transformers.map(t => this.getTransformerId(t)),
      });

      // Perform conversion using adapter
      const result = await LexicalAdapter.convertToMarkdown(
        document,
        transformers,
        mergedOptions
      );

      if (!result.success) {
        return result;
      }

      // Add our metadata to the result
      const finalResult = builder.success(result.data!);
      if (result.metadata) {
        finalResult.metadata = {
          ...finalResult.metadata,
          ...result.metadata,
        };
      }

      return finalResult;

    } catch (error) {
      return builder.error(
        'UNEXPECTED_ERROR',
        error instanceof Error ? error.message : 'Unknown error occurred',
        { originalError: error },
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Register default official transformers
   */
  private async registerDefaultTransformers(): Promise<void> {
    // Register standard transformers with priorities
    this.registry.registerOfficial('heading', HEADING, 100);
    this.registry.registerOfficial('quote', QUOTE, 90);
    this.registry.registerOfficial('unordered-list', UNORDERED_LIST, 80);
    this.registry.registerOfficial('ordered-list', ORDERED_LIST, 80);
    this.registry.registerOfficial('code', CODE, 110);
    this.registry.registerOfficial('inline-code', INLINE_CODE, 120);
    this.registry.registerOfficial('bold-star', BOLD_STAR, 100);
    this.registry.registerOfficial('bold-underscore', BOLD_UNDERSCORE, 100);
    this.registry.registerOfficial('italic-star', ITALIC_STAR, 90);
    this.registry.registerOfficial('italic-underscore', ITALIC_UNDERSCORE, 90);
    this.registry.registerOfficial('strikethrough', STRIKETHROUGH, 85);
    this.registry.registerOfficial('link', LINK, 95);

    // Register custom transformers for Ghost/Obsidian features
    await this.registerCustomTransformers();
  }

  /**
   * Register custom transformers for Ghost/Obsidian features
   */
  private async registerCustomTransformers(): Promise<void> {
    // Import custom transformers
    const { ENHANCED_QUOTE, CalloutNode } = await import('./transformers/ghost-callout');
    const { GHOST_BOOKMARK, BookmarkNode } = await import('./transformers/ghost-bookmark');
    const { ENHANCED_WIKILINK, WikilinkNode } = await import('./transformers/obsidian-wikilink');
    const { ENHANCED_TABLE, EnhancedTableCellNode } = await import('./transformers/table');
    const { MATH_INLINE, MATH_BLOCK, MATH_DISPLAY, MathNode } = await import('./transformers/math');

    // Register Ghost transformers
    this.registry.registerOfficial('ghost-callout', ENHANCED_QUOTE, 95, 'ghost');
    this.registry.registerOfficial('ghost-bookmark', GHOST_BOOKMARK, 85, 'ghost');

    // Register Obsidian transformers
    this.registry.registerOfficial('obsidian-wikilink', ENHANCED_WIKILINK, 100, 'obsidian');
    this.registry.registerOfficial('obsidian-math-inline', MATH_INLINE, 90, 'obsidian');
    this.registry.registerOfficial('obsidian-math-block', MATH_BLOCK, 90, 'obsidian');
    this.registry.registerOfficial('obsidian-math-display', MATH_DISPLAY, 89, 'obsidian');

    // Register shared transformers (both platforms)
    this.registry.registerOfficial('enhanced-table', ENHANCED_TABLE, 80, 'both');

    // Note: Node registration would be handled by the Lexical editor configuration
    // in a real implementation
  }

  /**
   * Get transformers based on options
   */
  private getTransformersForOptions(options: ConversionOptions): Transformer[] {
    const transformers: Transformer[] = [];

    // Always include standard transformers
    transformers.push(...this.registry.getOfficialTransformers());

    // Add platform-specific transformers based on options
    if (options.enableGhostFeatures) {
      const ghostTransformers = this.registry.getTransformersForPlatform('ghost');
      transformers.push(...ghostTransformers.map(ht => ht.transformer));
    }

    if (options.enableObsidianFeatures) {
      const obsidianTransformers = this.registry.getTransformersForPlatform('obsidian');
      transformers.push(...obsidianTransformers.map(ht => ht.transformer));
    }

    // Sort by priority (higher priority first)
    return transformers.sort((a, b) => {
      const aPriority = this.getTransformerPriority(a);
      const bPriority = this.getTransformerPriority(b);
      return bPriority - aPriority;
    });
  }

  /**
   * Get transformer ID for metadata
   */
  private getTransformerId(transformer: Transformer): string {
    // Try to find the transformer in our registry
    for (const [id, hybridTransformer] of this.registry['transformers']) {
      if (hybridTransformer.transformer === transformer) {
        return id;
      }
    }
    return `unknown-${transformer.type}`;
  }

  /**
   * Get transformer priority
   */
  private getTransformerPriority(transformer: Transformer): number {
    // Try to find the transformer in our registry
    for (const hybridTransformer of this.registry['transformers'].values()) {
      if (hybridTransformer.transformer === transformer) {
        return hybridTransformer.priority;
      }
    }
    return 0; // Default priority
  }

  /**
   * Get registry statistics
   */
  static getStats(): {
    registry: ReturnType<HybridTransformerRegistry['getStats']>;
    adapter: ReturnType<typeof LexicalAdapter.getPoolStats>;
  } {
    const instance = this.getInstance();
    return {
      registry: instance.registry.getStats(),
      adapter: LexicalAdapter.getPoolStats(),
    };
  }

  /**
   * Clear all caches and reset
   */
  static reset(): void {
    const instance = this.getInstance();
    instance.registry.clear();
    LexicalAdapter.clearPool();
    instance.initialized = false;
  }
}

// Export the main API functions for backward compatibility
export const markdownToLexical = HybridLexicalParser.markdownToLexical;
export const lexicalToMarkdown = HybridLexicalParser.lexicalToMarkdown;

// Export configuration functions
export const configure = HybridLexicalParser.configure;
export const getConfig = HybridLexicalParser.getConfig;
export const getStats = HybridLexicalParser.getStats;
export const reset = HybridLexicalParser.reset;
