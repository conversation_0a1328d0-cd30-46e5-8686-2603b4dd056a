/**
 * Hybrid transformer registry that supports both official Lexical transformers
 * and custom converter-style transformers
 */

import type {
  Transformer,
  ElementTransformer,
  MultilineElementTransformer,
  TextFormatTransformer,
  TextMatchTransformer,
} from '@lexical/markdown';
import type { NodeConverter } from '../types';
import type { TransformerConfig, ConversionResult, ConversionError } from './parser-config';

/**
 * Hybrid transformer that can wrap both official and custom transformers
 */
export interface HybridTransformer {
  id: string;
  type: 'official' | 'custom';
  transformer: Transformer;
  converter?: NodeConverter; // For backward compatibility
  priority: number;
  platform?: 'ghost' | 'obsidian' | 'both';
  enabled: boolean;
  nodeType: string;
}

/**
 * Registry for managing hybrid transformers
 */
export class HybridTransformerRegistry {
  private static instance: HybridTransformerRegistry;
  private transformers: Map<string, HybridTransformer> = new Map();
  private officialTransformers: Transformer[] = [];
  private customConverters: Map<string, NodeConverter> = new Map();
  private transformerCache: Map<string, HybridTransformer | null> = new Map();
  private isDirty = true;

  private constructor() {
    this.initializeDefaultTransformers();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): HybridTransformerRegistry {
    if (!this.instance) {
      this.instance = new HybridTransformerRegistry();
    }
    return this.instance;
  }

  /**
   * Register an official Lexical transformer
   */
  registerOfficial(
    id: string,
    transformer: Transformer,
    priority: number = 0,
    platform?: 'ghost' | 'obsidian' | 'both'
  ): void {
    const hybrid: HybridTransformer = {
      id,
      type: 'official',
      transformer,
      priority,
      platform,
      enabled: true,
      nodeType: this.inferNodeType(transformer),
    };

    this.transformers.set(id, hybrid);
    this.markDirty();
  }

  /**
   * Register a custom converter (backward compatibility)
   */
  registerCustom(
    id: string,
    converter: NodeConverter,
    priority: number = 0,
    platform?: 'ghost' | 'obsidian' | 'both'
  ): void {
    const wrappedTransformer = this.wrapConverter(converter);
    const hybrid: HybridTransformer = {
      id,
      type: 'custom',
      transformer: wrappedTransformer,
      converter,
      priority,
      platform,
      enabled: true,
      nodeType: this.inferNodeTypeFromConverter(converter),
    };

    this.transformers.set(id, hybrid);
    this.customConverters.set(id, converter);
    this.markDirty();
  }

  /**
   * Register transformer from configuration
   */
  registerFromConfig(config: TransformerConfig): ConversionResult<void> {
    try {
      // This would be implemented to create transformers from JSON config
      // For now, return success
      return { success: true };
    } catch (error) {
      return {
        success: false,
        errors: [{
          code: 'TRANSFORMER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to register transformer',
          stack: error instanceof Error ? error.stack : undefined,
        }],
      };
    }
  }

  /**
   * Get all official transformers for Lexical functions
   */
  getOfficialTransformers(): Transformer[] {
    if (this.isDirty) {
      this.rebuildOfficialArray();
    }
    return [...this.officialTransformers];
  }

  /**
   * Get transformer by ID
   */
  getTransformer(id: string): HybridTransformer | null {
    return this.transformers.get(id) || null;
  }

  /**
   * Get transformer by node type (backward compatibility)
   */
  getTransformerByNodeType(nodeType: string): HybridTransformer | null {
    // Check cache first
    if (this.transformerCache.has(nodeType)) {
      return this.transformerCache.get(nodeType) || null;
    }

    // Find transformer for node type
    for (const transformer of this.transformers.values()) {
      if (transformer.enabled && transformer.nodeType === nodeType) {
        this.transformerCache.set(nodeType, transformer);
        return transformer;
      }
    }

    // Try canHandle for custom converters
    for (const transformer of this.transformers.values()) {
      if (transformer.enabled && transformer.converter?.canHandle(nodeType)) {
        this.transformerCache.set(nodeType, transformer);
        return transformer;
      }
    }

    this.transformerCache.set(nodeType, null);
    return null;
  }

  /**
   * Get converter by node type (backward compatibility)
   */
  getConverter(nodeType: string): NodeConverter | null {
    const transformer = this.getTransformerByNodeType(nodeType);
    return transformer?.converter || null;
  }

  /**
   * Get transformers for specific platform
   */
  getTransformersForPlatform(platform: 'ghost' | 'obsidian' | 'both'): HybridTransformer[] {
    return Array.from(this.transformers.values())
      .filter(t => t.enabled && (t.platform === platform || t.platform === 'both'))
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * Enable/disable transformer
   */
  setTransformerEnabled(id: string, enabled: boolean): void {
    const transformer = this.transformers.get(id);
    if (transformer) {
      transformer.enabled = enabled;
      this.markDirty();
    }
  }

  /**
   * Clear all transformers
   */
  clear(): void {
    this.transformers.clear();
    this.customConverters.clear();
    this.transformerCache.clear();
    this.officialTransformers = [];
    this.isDirty = true;
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    totalTransformers: number;
    officialTransformers: number;
    customTransformers: number;
    enabledTransformers: number;
    cacheSize: number;
  } {
    const enabled = Array.from(this.transformers.values()).filter(t => t.enabled);
    const official = enabled.filter(t => t.type === 'official');
    const custom = enabled.filter(t => t.type === 'custom');

    return {
      totalTransformers: this.transformers.size,
      officialTransformers: official.length,
      customTransformers: custom.length,
      enabledTransformers: enabled.length,
      cacheSize: this.transformerCache.size,
    };
  }

  /**
   * Initialize default transformers
   */
  private initializeDefaultTransformers(): void {
    // Import official transformers
    import('@lexical/markdown').then(({ 
      HEADING,
      QUOTE,
      UNORDERED_LIST,
      ORDERED_LIST,
      CODE,
      INLINE_CODE,
      BOLD_STAR,
      BOLD_UNDERSCORE,
      ITALIC_STAR,
      ITALIC_UNDERSCORE,
      STRIKETHROUGH,
      LINK,
    }) => {
      // Register standard transformers
      this.registerOfficial('heading', HEADING, 100);
      this.registerOfficial('quote', QUOTE, 90);
      this.registerOfficial('unordered-list', UNORDERED_LIST, 80);
      this.registerOfficial('ordered-list', ORDERED_LIST, 80);
      this.registerOfficial('code', CODE, 110);
      this.registerOfficial('inline-code', INLINE_CODE, 120);
      this.registerOfficial('bold-star', BOLD_STAR, 100);
      this.registerOfficial('bold-underscore', BOLD_UNDERSCORE, 100);
      this.registerOfficial('italic-star', ITALIC_STAR, 90);
      this.registerOfficial('italic-underscore', ITALIC_UNDERSCORE, 90);
      this.registerOfficial('strikethrough', STRIKETHROUGH, 85);
      this.registerOfficial('link', LINK, 95);
    }).catch(error => {
      console.error('Failed to load official transformers:', error);
    });
  }

  /**
   * Wrap a custom converter to work as a transformer
   */
  private wrapConverter(converter: NodeConverter): ElementTransformer {
    return {
      dependencies: [], // Would need to be inferred or configured
      export: (node, traverseChildren) => {
        try {
          return converter.lexicalToMarkdown(node);
        } catch (error) {
          console.warn(`Converter export error for ${node.type}:`, error);
          return null;
        }
      },
      regExp: /.*/, // Would need to be inferred from converter
      replace: (parentNode, children, match, isImport) => {
        try {
          // This is a simplified wrapper - would need more sophisticated bridging
          const markdownNode = this.createMarkdownNodeFromMatch(children, match);
          const lexicalNode = converter.markdownToLexical(markdownNode);
          
          if (lexicalNode) {
            if (Array.isArray(lexicalNode)) {
              parentNode.append(...lexicalNode);
            } else {
              parentNode.append(lexicalNode);
            }
          }
        } catch (error) {
          console.warn(`Converter replace error:`, error);
        }
      },
      type: 'element',
    };
  }

  /**
   * Infer node type from transformer
   */
  private inferNodeType(transformer: Transformer): string {
    // This would analyze the transformer to determine what node type it handles
    // For now, return a generic type
    return 'unknown';
  }

  /**
   * Infer node type from converter
   */
  private inferNodeTypeFromConverter(converter: NodeConverter): string {
    // This would analyze the converter to determine what node type it handles
    // For now, return a generic type
    return 'unknown';
  }

  /**
   * Create markdown node from match (simplified)
   */
  private createMarkdownNodeFromMatch(children: any[], match: string[]): any {
    // This would create a proper markdown node from the match
    // For now, return a basic structure
    return {
      type: 'unknown',
      children,
      value: match[0],
    };
  }

  /**
   * Mark registry as dirty (needs rebuild)
   */
  private markDirty(): void {
    this.isDirty = true;
    this.transformerCache.clear();
  }

  /**
   * Rebuild official transformers array
   */
  private rebuildOfficialArray(): void {
    this.officialTransformers = Array.from(this.transformers.values())
      .filter(t => t.enabled && t.type === 'official')
      .sort((a, b) => b.priority - a.priority)
      .map(t => t.transformer);
    
    this.isDirty = false;
  }
}

/**
 * Utility functions for transformer management
 */
export class TransformerUtils {
  /**
   * Create transformer from configuration
   */
  static createFromConfig(config: TransformerConfig): ConversionResult<Transformer> {
    try {
      // This would implement the actual transformer creation from config
      // For now, return an error indicating it's not implemented
      return {
        success: false,
        errors: [{
          code: 'TRANSFORMER_ERROR',
          message: 'Transformer creation from config not yet implemented',
        }],
      };
    } catch (error) {
      return {
        success: false,
        errors: [{
          code: 'TRANSFORMER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create transformer',
          stack: error instanceof Error ? error.stack : undefined,
        }],
      };
    }
  }

  /**
   * Validate transformer compatibility
   */
  static validateTransformer(transformer: Transformer): ConversionResult<boolean> {
    try {
      // Basic validation
      if (!transformer.type) {
        return {
          success: false,
          errors: [{
            code: 'INVALID_INPUT',
            message: 'Transformer must have a type',
          }],
        };
      }

      // Type-specific validation
      switch (transformer.type) {
        case 'element':
          return this.validateElementTransformer(transformer as ElementTransformer);
        case 'multiline-element':
          return this.validateMultilineElementTransformer(transformer as MultilineElementTransformer);
        case 'text-format':
          return this.validateTextFormatTransformer(transformer as TextFormatTransformer);
        case 'text-match':
          return this.validateTextMatchTransformer(transformer as TextMatchTransformer);
        default:
          return {
            success: false,
            errors: [{
              code: 'INVALID_INPUT',
              message: `Unknown transformer type: ${transformer.type}`,
            }],
          };
      }
    } catch (error) {
      return {
        success: false,
        errors: [{
          code: 'UNEXPECTED_ERROR',
          message: error instanceof Error ? error.message : 'Validation error',
          stack: error instanceof Error ? error.stack : undefined,
        }],
      };
    }
  }

  private static validateElementTransformer(transformer: ElementTransformer): ConversionResult<boolean> {
    if (!transformer.regExp || !transformer.replace || !transformer.export) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: 'ElementTransformer must have regExp, replace, and export functions',
        }],
      };
    }
    return { success: true, data: true };
  }

  private static validateMultilineElementTransformer(transformer: MultilineElementTransformer): ConversionResult<boolean> {
    if (!transformer.regExpStart || !transformer.replace) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: 'MultilineElementTransformer must have regExpStart and replace functions',
        }],
      };
    }
    return { success: true, data: true };
  }

  private static validateTextFormatTransformer(transformer: TextFormatTransformer): ConversionResult<boolean> {
    if (!transformer.format || !transformer.tag) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: 'TextFormatTransformer must have format and tag',
        }],
      };
    }
    return { success: true, data: true };
  }

  private static validateTextMatchTransformer(transformer: TextMatchTransformer): ConversionResult<boolean> {
    if (!transformer.importRegExp || !transformer.replace || !transformer.trigger) {
      return {
        success: false,
        errors: [{
          code: 'INVALID_INPUT',
          message: 'TextMatchTransformer must have importRegExp, replace, and trigger',
        }],
      };
    }
    return { success: true, data: true };
  }
}
