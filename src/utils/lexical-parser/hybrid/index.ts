/**
 * Main exports for the hybrid Lexical parser system
 * Provides backward compatibility while leveraging official @lexical/markdown package
 */

// Main parser exports
export {
  HybridLexicalParser,
  markdownToLexical,
  lexicalToMarkdown,
  configure,
  getConfig,
  getStats,
  reset,
} from './hybrid-parser';

// Configuration exports
export type {
  ParserConfig,
  ConversionOptions,
  PerformanceConfig,
  TransformerConfig,
  Extension,
} from './parser-config';
export {
  ParserConfigManager,
  ConfigUtils,
  DEFAULT_PERFORMANCE_CONFIG,
  DEFAULT_CONVERSION_OPTIONS,
} from './parser-config';

// Result types exports
export type {
  ConversionResult,
  ConversionError,
  ConversionMetadata,
  MarkdownToLexicalResult,
  LexicalToMarkdownResult,
  ConversionErrorCode,
} from './conversion-types';
export {
  ConversionResultBuilder,
  ConversionResultUtils,
} from './conversion-types';

// Registry exports
export type {
  HybridTransformer,
} from './transformer-registry';
export {
  HybridTransformerRegistry,
  TransformerUtils,
} from './transformer-registry';

// Adapter exports
export {
  LexicalAdapter,
} from './lexical-adapter';

// Validation exports
export {
  ConfigValidator,
} from './config-validator';

// Re-export types from the original parser for compatibility
export type {
  LexicalDocument,
  LexicalRootNode,
  LexicalNode,
  LexicalParagraphNode,
  LexicalHeadingNode,
  LexicalTextNode,
  LexicalListNode,
  LexicalListItemNode,
  LexicalQuoteNode,
  LexicalCodeNode,
  LexicalLinkNode,
  MarkdownAST,
  MarkdownNode,
  NodeConverter,
} from '../types';

/**
 * Initialize the hybrid parser with default configuration
 * This function maintains backward compatibility with the existing API
 */
export async function initializeHybridParser(config?: Partial<ParserConfig>): Promise<void> {
  const parser = HybridLexicalParser.getInstance();
  await parser.initialize(config);
}

/**
 * Backward compatibility: Export the main parser class with the original name
 * This allows existing code to continue working without changes
 */
export { HybridLexicalParser as LexicalMarkdownParser };

/**
 * Default configuration for easy setup
 */
export const DEFAULT_HYBRID_CONFIG: ParserConfig = {
  transformers: [], // Will be populated by the registry
  options: DEFAULT_CONVERSION_OPTIONS,
  extensions: [
    {
      name: 'ghost-features',
      platform: 'ghost',
      transformers: [],
      enabled: true,
    },
    {
      name: 'obsidian-features',
      platform: 'obsidian',
      transformers: [],
      enabled: true,
    },
  ],
};

/**
 * Utility function to create a basic configuration
 */
export function createBasicConfig(
  enableGhost = true,
  enableObsidian = true,
  performanceOptions?: Partial<PerformanceConfig>
): ParserConfig {
  return {
    transformers: [],
    options: {
      ...DEFAULT_CONVERSION_OPTIONS,
      enableGhostFeatures: enableGhost,
      enableObsidianFeatures: enableObsidian,
      performance: {
        ...DEFAULT_PERFORMANCE_CONFIG,
        ...performanceOptions,
      },
    },
    extensions: [
      {
        name: 'ghost-features',
        platform: 'ghost',
        transformers: [],
        enabled: enableGhost,
      },
      {
        name: 'obsidian-features',
        platform: 'obsidian',
        transformers: [],
        enabled: enableObsidian,
      },
    ],
  };
}

/**
 * Utility function to validate and load configuration from JSON
 */
export async function loadConfigFromJSON(jsonString: string): Promise<ConversionResult<ParserConfig>> {
  const validation = ConfigValidator.validateJSONConfig(jsonString);
  if (validation.success && validation.data) {
    // Initialize parser with the loaded configuration
    await initializeHybridParser(validation.data);
  }
  return validation;
}

/**
 * Utility function to get performance metrics
 */
export function getPerformanceMetrics(): {
  registry: ReturnType<HybridTransformerRegistry['getStats']>;
  adapter: ReturnType<typeof LexicalAdapter.getPoolStats>;
  config: ParserConfig;
} {
  return {
    ...getStats(),
    config: getConfig(),
  };
}

/**
 * Utility function for debugging - exports current state as JSON
 */
export function exportDebugInfo(): string {
  const stats = getPerformanceMetrics();
  const debugInfo = {
    timestamp: new Date().toISOString(),
    version: '1.0.0-hybrid',
    stats,
    transformers: stats.registry,
  };
  
  return JSON.stringify(debugInfo, null, 2);
}

/**
 * Migration helper: Check if the hybrid parser is being used
 */
export function isHybridMode(): boolean {
  try {
    const parser = HybridLexicalParser.getInstance();
    return parser['initialized'] || false;
  } catch {
    return false;
  }
}

/**
 * Migration helper: Get compatibility status
 */
export function getCompatibilityStatus(): {
  hybridMode: boolean;
  officialTransformers: number;
  customTransformers: number;
  totalTransformers: number;
} {
  const stats = getStats();
  return {
    hybridMode: isHybridMode(),
    officialTransformers: stats.registry.officialTransformers,
    customTransformers: stats.registry.customTransformers,
    totalTransformers: stats.registry.totalTransformers,
  };
}
