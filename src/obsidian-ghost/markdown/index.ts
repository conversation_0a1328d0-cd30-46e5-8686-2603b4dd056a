/**
 * Obsidian-Ghost Markdown Parser
 *
 * A clean, simple parser that converts between Markdown and Lexical
 * with support for Obsidian-specific formatting and Ghost-specific nodes.
 */

import { createEditor, type LexicalEditor } from 'lexical';
import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html';
import { $convertFromMarkdownString, $convertToMarkdownString } from '@lexical/markdown';
import { TRANSFORMERS } from '@lexical/markdown';

/**
 * Result of a conversion operation
 */
export interface ConversionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Lexical document representation
 */
export interface LexicalDocument {
  root: any; // Will be properly typed later
  nodes: any[]; // Will be properly typed later
}

/**
 * Main Markdown parser class
 */
export class Markdown {
  private editor: LexicalEditor;

  constructor() {
    // Create a headless Lexical editor
    this.editor = createEditor({
      namespace: 'obsidian-ghost-markdown',
      onError: (error) => {
        console.error('Lexical editor error:', error);
      },
    });
  }

  /**
   * Convert markdown string to Lexical document
   */
  async markdownToLexical(markdown: string): Promise<ConversionResult<LexicalDocument>> {
    try {
      if (typeof markdown !== 'string') {
        return {
          success: false,
          error: 'Input must be a string',
        };
      }

      return await new Promise((resolve) => {
        this.editor.update(() => {
          try {
            // Use Lexical's built-in markdown converter with basic transformers
            // This function modifies the editor state directly
            $convertFromMarkdownString(markdown, TRANSFORMERS);
          } catch (error) {
            resolve({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
            return;
          }
        });

        // After the update, get the editor state
        const editorState = this.editor.getEditorState();
        const document: LexicalDocument = {
          root: editorState.toJSON(),
          nodes: [], // Will populate this properly later
        };

        resolve({
          success: true,
          data: document,
        });
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Convert Lexical document to markdown string
   */
  async lexicalToMarkdown(document: LexicalDocument): Promise<ConversionResult<string>> {
    try {
      if (!document || !document.root) {
        return {
          success: false,
          error: 'Invalid document structure',
        };
      }

      // Handle empty documents
      if (!document.root.children || document.root.children.length === 0) {
        return {
          success: true,
          data: '',
        };
      }

      return await new Promise((resolve) => {
        this.editor.update(() => {
          try {
            // Restore the editor state from the document
            const editorState = this.editor.parseEditorState(document.root);
            this.editor.setEditorState(editorState);

            // Convert to markdown using Lexical's built-in converter
            const markdown = $convertToMarkdownString(TRANSFORMERS);

            resolve({
              success: true,
              data: markdown,
            });
          } catch (error) {
            resolve({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        });
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    // Clean up the editor if needed
  }
}

/**
 * Convenience functions for one-off conversions
 */

/**
 * Convert markdown to Lexical document
 */
export async function markdownToLexical(markdown: string): Promise<ConversionResult<LexicalDocument>> {
  const parser = new Markdown();
  try {
    return await parser.markdownToLexical(markdown);
  } finally {
    parser.destroy();
  }
}

/**
 * Convert Lexical document to markdown
 */
export async function lexicalToMarkdown(document: LexicalDocument): Promise<ConversionResult<string>> {
  const parser = new Markdown();
  try {
    return await parser.lexicalToMarkdown(document);
  } finally {
    parser.destroy();
  }
}

/**
 * Round-trip test: markdown -> lexical -> markdown
 */
export async function roundTrip(markdown: string): Promise<ConversionResult<string>> {
  const parser = new Markdown();
  try {
    const lexicalResult = await parser.markdownToLexical(markdown);
    if (!lexicalResult.success || !lexicalResult.data) {
      return {
        success: false,
        error: `Failed to convert to Lexical: ${lexicalResult.error}`,
      };
    }

    const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data);
    if (!markdownResult.success || markdownResult.data === undefined) {
      return {
        success: false,
        error: `Failed to convert back to markdown: ${markdownResult.error}`,
      };
    }

    return {
      success: true,
      data: markdownResult.data,
    };
  } finally {
    parser.destroy();
  }
}
